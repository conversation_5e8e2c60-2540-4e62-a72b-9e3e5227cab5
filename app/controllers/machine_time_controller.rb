class MachineTimeController < ApplicationController
  before_action :set_layout_container_class

  def index
    @current_tab = params[:tab] || 'cpu'
    @page = params[:page] || 1

    # 获取机时记录数据（根据tab类型）
    @machine_time_records = get_machine_time_records(@current_tab)

    # 分页处理
    @machine_time_records = Kaminari.paginate_array(@machine_time_records).page(@page).per(10)
  end

  private

  def set_layout_container_class
    @layout_container_class = 'container-md'
  end

  # 获取机时记录数据（CPU、GPU和RAM都使用真实数据）
  def get_machine_time_records(tab_type)
    case tab_type
    when 'cpu'
      get_real_cpu_usage
    when 'gpu'
      get_real_gpu_usage
    when 'ram'
      get_real_memory_usage
    else
      get_real_cpu_usage
    end
  end

  # CPU使用记录模拟数据
  def get_cpu_records
    [
      {
        task_name: '任务名称1',
        usage_time: '2核',
        start_time: '2025.04.12 12:24:56',
        remaining_time: '1:12:58',
        remaining_machine_time: '0:58:12'
      },
      {
        task_name: '任务名称2',
        usage_time: '4核',
        start_time: '2025.04.12 11:30:22',
        remaining_time: '2:45:30',
        remaining_machine_time: '1:25:45'
      },
      {
        task_name: '任务名称3',
        usage_time: '8核',
        start_time: '2025.04.12 10:15:10',
        remaining_time: '0:30:15',
        remaining_machine_time: '0:15:30'
      },
      {
        task_name: '任务名称4',
        usage_time: '2核',
        start_time: '2025.04.12 09:45:33',
        remaining_time: '3:20:45',
        remaining_machine_time: '2:10:20'
      },
      {
        task_name: '任务名称5',
        usage_time: '6核',
        start_time: '2025.04.12 08:20:15',
        remaining_time: '1:55:12',
        remaining_machine_time: '0:45:30'
      },
      {
        task_name: '任务名称6',
        usage_time: '2核',
        start_time: '2025.04.11 16:30:45',
        remaining_time: '4:15:20',
        remaining_machine_time: '3:30:15'
      },
      {
        task_name: '任务名称7',
        usage_time: '12核',
        start_time: '2025.04.11 14:20:30',
        remaining_time: '0:45:10',
        remaining_machine_time: '0:20:05'
      },
      {
        task_name: '任务名称8',
        usage_time: '4核',
        start_time: '2025.04.11 12:15:20',
        remaining_time: '2:30:45',
        remaining_machine_time: '1:15:30'
      },
      {
        task_name: '任务名称9',
        usage_time: '8核',
        start_time: '2025.04.11 10:45:15',
        remaining_time: '1:20:30',
        remaining_machine_time: '0:40:15'
      },
      {
        task_name: '任务名称10',
        usage_time: '2核',
        start_time: '2025.04.11 09:30:10',
        remaining_time: '5:10:25',
        remaining_machine_time: '4:20:15'
      },
      {
        task_name: '任务名称11',
        usage_time: '16核',
        start_time: '2025.04.10 18:20:45',
        remaining_time: '0:25:15',
        remaining_machine_time: '0:10:30'
      },
      {
        task_name: '任务名称12',
        usage_time: '6核',
        start_time: '2025.04.10 15:45:30',
        remaining_time: '3:45:20',
        remaining_machine_time: '2:30:10'
      }
    ]
  end

  # GPU使用记录模拟数据
  def get_gpu_records
    [
      {
        task_name: 'GPU任务1',
        usage_time: '1GPU',
        start_time: '2025.04.12 12:24:56',
        remaining_time: '2:12:58',
        remaining_machine_time: '1:58:12'
      },
      {
        task_name: 'GPU任务2',
        usage_time: '2GPU',
        start_time: '2025.04.12 11:30:22',
        remaining_time: '1:45:30',
        remaining_machine_time: '0:55:45'
      },
      {
        task_name: 'GPU任务3',
        usage_time: '4GPU',
        start_time: '2025.04.12 10:15:10',
        remaining_time: '3:30:15',
        remaining_machine_time: '2:45:30'
      },
      {
        task_name: 'GPU任务4',
        usage_time: '1GPU',
        start_time: '2025.04.12 09:45:33',
        remaining_time: '0:50:45',
        remaining_machine_time: '0:30:20'
      },
      {
        task_name: 'GPU任务5',
        usage_time: '8GPU',
        start_time: '2025.04.12 08:20:15',
        remaining_time: '4:25:12',
        remaining_machine_time: '3:15:30'
      }
    ]
  end

  # RAM使用记录模拟数据
  def get_ram_records
    [
      {
        task_name: 'RAM任务1',
        usage_time: '32GB',
        start_time: '2025.04.12 12:24:56',
        remaining_time: '1:42:58',
        remaining_machine_time: '1:18:12'
      },
      {
        task_name: 'RAM任务2',
        usage_time: '64GB',
        start_time: '2025.04.12 11:30:22',
        remaining_time: '2:15:30',
        remaining_machine_time: '1:45:45'
      },
      {
        task_name: 'RAM任务3',
        usage_time: '128GB',
        start_time: '2025.04.12 10:15:10',
        remaining_time: '0:45:15',
        remaining_machine_time: '0:25:30'
      },
      {
        task_name: 'RAM任务4',
        usage_time: '16GB',
        start_time: '2025.04.12 09:45:33',
        remaining_time: '3:10:45',
        remaining_machine_time: '2:40:20'
      },
      {
        task_name: 'RAM任务5',
        usage_time: '256GB',
        start_time: '2025.04.12 08:20:15',
        remaining_time: '5:35:12',
        remaining_machine_time: '4:55:30'
      }
    ]
  end

  # 获取真实的CPU使用情况（基于sacct命令）
  def get_real_cpu_usage
    begin
      # 执行sacct命令获取作业信息
      # 格式: JobName,user,AllocCPUS,CPUTime
      cmd = "sacct --allusers --starttime 2023-01-01 --endtime now --format=JobName,user,AllocCPUS,CPUTime --parsable2 --noheader"
      output = `#{cmd} 2>/dev/null`

      records = []

      # 解析输出
      output.split("\n").each do |line|
        next if line.strip.empty?

        # 过滤条件：包含admin用户，排除batch和.0结尾的作业
        next unless line.include?(@user.name)
        next if line.include?('batch')
        next if line.match(/\.\d+$/)

        fields = line.split('|')
        next if fields.length < 4

        job_name = fields[0]
        user = fields[1]
        alloc_cpus = fields[2]
        cpu_time = fields[3]

        # 转换为与get_cpu_records相同的格式
        records << {
          task_name: job_name,
          usage_time: "#{alloc_cpus}核",
          start_time: Time.now.strftime('%Y.%m.%d %H:%M:%S'), # sacct不直接提供开始时间，使用当前时间作为占位符
          remaining_time: cpu_time, # 使用CPUTime作为已用时间
          remaining_machine_time: calculate_machine_time(alloc_cpus.to_i, cpu_time)
        }
      end

      records
    rescue => e
      Rails.logger.error "获取CPU使用情况失败: #{e.message}"
      # 如果命令执行失败，返回空数组或默认数据
      []
    end
  end

  private

  # 计算机时（CPU核数 × 时间）
  def calculate_machine_time(cpu_count, cpu_time)
    begin
      # 解析时间格式 HH:MM:SS
      time_parts = cpu_time.split(':')
      return cpu_time if time_parts.length != 3

      hours = time_parts[0].to_i
      minutes = time_parts[1].to_i
      seconds = time_parts[2].to_i

      # 计算总秒数
      total_seconds = hours * 3600 + minutes * 60 + seconds

      # 计算机时（总秒数 × CPU核数）
      machine_time_seconds = total_seconds * cpu_count

      # 转换回时间格式
      machine_hours = machine_time_seconds / 3600
      machine_minutes = (machine_time_seconds % 3600) / 60
      machine_secs = machine_time_seconds % 60

      sprintf("%d:%02d:%02d", machine_hours, machine_minutes, machine_secs)
    rescue
      cpu_time # 如果计算失败，返回原始时间
    end
  end

  # 解析GPU信息从ReqTRES字段
  def parse_gpu_info(req_tres)
    begin
      # 从ReqTRES中提取GPU信息
      # 例如: "billing=4,cpu=4,gres/gpu:a100.1g10gb=1,mem=128600M,node=1"
      gpu_match = req_tres.match(/gres\/gpu:([^=]+)=(\d+)/)
      return nil unless gpu_match

      gpu_type = gpu_match[1] # 例如: "a100.1g10gb"
      gpu_count = gpu_match[2].to_i # 例如: 1

      # 格式化GPU信息，类似于get_gpu_records的格式
      if gpu_count == 1
        "1GPU(#{gpu_type})"
      else
        "#{gpu_count}GPU(#{gpu_type})"
      end
    rescue
      nil
    end
  end

  # 计算GPU机时（GPU数量 × 时间）
  def calculate_gpu_machine_time(gpu_info, elapsed_time)
    begin
      # 从gpu_info中提取GPU数量
      gpu_count_match = gpu_info.match(/(\d+)GPU/)
      return elapsed_time unless gpu_count_match

      gpu_count = gpu_count_match[1].to_i

      # 解析时间格式 HH:MM:SS
      time_parts = elapsed_time.split(':')
      return elapsed_time if time_parts.length != 3

      hours = time_parts[0].to_i
      minutes = time_parts[1].to_i
      seconds = time_parts[2].to_i

      # 计算总秒数
      total_seconds = hours * 3600 + minutes * 60 + seconds

      # 计算GPU机时（总秒数 × GPU数量）
      machine_time_seconds = total_seconds * gpu_count

      # 转换回时间格式
      machine_hours = machine_time_seconds / 3600
      machine_minutes = (machine_time_seconds % 3600) / 60
      machine_secs = machine_time_seconds % 60

      sprintf("%d:%02d:%02d", machine_hours, machine_minutes, machine_secs)
    rescue
      elapsed_time # 如果计算失败，返回原始时间
    end
  end

  # 解析内存信息从ReqMem字段
  def parse_memory_info(req_mem)
    begin
      # 从ReqMem中提取内存信息
      # 例如: "128600M" 或 "128600Mc" 或 "128600Mn"
      memory_match = req_mem.match(/(\d+)([KMGT]?)([cn]?)/)
      return nil unless memory_match

      memory_value = memory_match[1].to_i
      memory_unit = memory_match[2] # K, M, G, T
      memory_type = memory_match[3] # c=per-cpu, n=per-node, 空=total

      # 转换为GB单位，类似于get_ram_records的格式
      case memory_unit
      when 'K'
        memory_gb = (memory_value / 1024.0 / 1024.0).round(2)
      when 'M', ''
        memory_gb = (memory_value / 1024.0).round(2)
      when 'G'
        memory_gb = memory_value
      when 'T'
        memory_gb = memory_value * 1024
      else
        memory_gb = (memory_value / 1024.0).round(2) # 默认按M处理
      end

      # 格式化内存信息
      if memory_gb >= 1024
        "#{(memory_gb / 1024.0).round(2)}TB"
      elsif memory_gb >= 1
        "#{memory_gb.to_i}GB"
      else
        "#{(memory_gb * 1024).to_i}MB"
      end
    rescue
      nil
    end
  end

  # 计算内存机时（内存大小 × 时间）
  def calculate_memory_machine_time(memory_info, elapsed_time)
    begin
      # 从memory_info中提取内存大小（以GB为单位）
      memory_match = memory_info.match(/(\d+(?:\.\d+)?)([KMGT]?B)/)
      return elapsed_time unless memory_match

      memory_value = memory_match[1].to_f
      memory_unit = memory_match[2]

      # 转换为GB
      memory_gb = case memory_unit
                  when 'MB'
                    memory_value / 1024.0
                  when 'GB'
                    memory_value
                  when 'TB'
                    memory_value * 1024
                  else
                    memory_value # 默认GB
                  end

      # 解析时间格式 HH:MM:SS
      time_parts = elapsed_time.split(':')
      return elapsed_time if time_parts.length != 3

      hours = time_parts[0].to_i
      minutes = time_parts[1].to_i
      seconds = time_parts[2].to_i

      # 计算总秒数
      total_seconds = hours * 3600 + minutes * 60 + seconds

      # 计算内存机时（总秒数，这里不乘以内存大小，因为内存机时通常按时间计算）
      # 如果需要按内存大小计算，可以取消下面的注释
      # machine_time_seconds = total_seconds * memory_gb.to_i
      machine_time_seconds = total_seconds

      # 转换回时间格式
      machine_hours = machine_time_seconds / 3600
      machine_minutes = (machine_time_seconds % 3600) / 60
      machine_secs = machine_time_seconds % 60

      sprintf("%d:%02d:%02d", machine_hours, machine_minutes, machine_secs)
    rescue
      elapsed_time # 如果计算失败，返回原始时间
    end
  end

  # 获取真实的GPU使用情况（基于sacct命令）
  def get_real_gpu_usage
    begin
      # 执行sacct命令获取GPU作业信息
      # 格式: JobName,user,ReqTRES,Elapsed
      cmd = "sacct --allusers --starttime 2024-01-01 --endtime now --format=JobName,user,ReqTRES,Elapsed --parsable2 --noheader"
      output = `#{cmd} 2>/dev/null`

      records = []

      # 解析输出
      output.split("\n").each do |line|
        next if line.strip.empty?

        # 过滤条件：包含gpu和admin用户
        next unless line.include?('gpu') && line.include?(@user.name)

        fields = line.split('|')
        next if fields.length < 4

        job_name = fields[0]
        user = fields[1]
        req_tres = fields[2]
        elapsed = fields[3]

        # 解析GPU信息从ReqTRES字段
        gpu_info = parse_gpu_info(req_tres)
        next if gpu_info.nil?

        # 转换为与get_gpu_records相同的格式
        records << {
          task_name: job_name,
          usage_time: gpu_info,
          start_time: Time.now.strftime('%Y.%m.%d %H:%M:%S'), # 使用当前时间作为占位符
          remaining_time: elapsed, # 使用Elapsed作为已用时间
          remaining_machine_time: calculate_gpu_machine_time(gpu_info, elapsed)
        }
      end

      records
    rescue => e
      Rails.logger.error "获取GPU使用情况失败: #{e.message}"
      # 如果命令执行失败，返回空数组
      []
    end
  end

  # 获取真实的内存使用情况（基于sacct命令）
  def get_real_memory_usage
    begin
      # 执行sacct命令获取内存作业信息
      # 格式: JobName,user,ReqMem,Elapsed
      cmd = "sacct --allusers --starttime 2024-01-01 --endtime now --format=JobName,user,ReqMem,Elapsed --parsable2 --noheader"
      output = `#{cmd} 2>/dev/null`

      records = []

      # 解析输出
      output.split("\n").each do |line|
        next if line.strip.empty?

        # 过滤条件：包含admin用户，排除batch和.0结尾的作业
        next unless line.include?(@user.name)
        next if line.include?('batch')
        next if line.match(/\.\d+$/)

        fields = line.split('|')
        next if fields.length < 4

        job_name = fields[0]
        user = fields[1]
        req_mem = fields[2]
        elapsed = fields[3]

        # 解析内存信息
        memory_info = parse_memory_info(req_mem)
        next if memory_info.nil?

        # 转换为与get_ram_records相同的格式
        records << {
          task_name: job_name,
          usage_time: memory_info,
          start_time: Time.now.strftime('%Y.%m.%d %H:%M:%S'), # 使用当前时间作为占位符
          remaining_time: elapsed, # 使用Elapsed作为已用时间
          remaining_machine_time: calculate_memory_machine_time(memory_info, elapsed)
        }
      end

      records
    rescue => e
      Rails.logger.error "获取内存使用情况失败: #{e.message}"
      # 如果命令执行失败，返回空数组
      []
    end
  end
end
