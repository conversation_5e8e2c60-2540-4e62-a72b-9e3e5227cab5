class ScreenController < ApplicationController
  def index
    render layout: "screen/index"
  end

  def get_data
    render json: JSON.parse(SystemDatum.first.data || {})
  end

  def get_right1_data
    # 获取系统资源数据用于图表显示
    system_data = JSON.parse(SystemDatum.first.data || {})["resourceUsage"]
    data = {
      nodes: {
        total: system_data[0]["total"],
        percent: system_data[0]["value"],
        free: system_data[0]["free"]
      },
      cpu: {
        total: system_data[1]["total"],
        percent: system_data[1]["value"],
        free: system_data[1]["free"]
      },
      gpu: {
        total: system_data[2]["total"],
        percent: system_data[2]["value"],
        free: system_data[2]["free"]
      },
      ram: {
        total: system_data[3]["total"],
        percent: system_data[3]["value"],
        free: system_data[3]["free"]
      },
      disk: {
        total: system_data[4]["total"],
        percent: system_data[4]["value"],
        free: system_data[4]["free"]
      }
    }

    render json: data
  end
end
