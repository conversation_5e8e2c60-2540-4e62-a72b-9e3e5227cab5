require 'csv'
class AdminDashboardController < ApplicationController
  before_action :check_authorization
  skip_before_action :verify_authenticity_token, only: [:license_upload, :license_delete]
  before_action :set_layout_container_class, only: [:statistics, :user_manage, :cluster_manage, :user_bills, :license_manage]

  def index
    render layout: "admin/index"
  end

  def screen
    # code
  end

  # 集群资源统计
  def statistics
    @idle_nodes_count, @drain_nodes_count, @down_nodes_count = UsageType.single_usage
    @cpu_usage, @cpu_usage_rencent, @memory_usage, @memory_usage_rencent, @available_storage_usage, @available_storage_rencent, @title_arr = UsageType.total_usage
  end

  # 用户管理
  def user_manage
    if params[:label].present? && params[:username].present?
      if params[:label] == "用户账号"
        @query_user_list = Kaminari.paginate_array(CustomLdap.query_user_list(params[:username])).page(params[:page]).per(10)
      else
        uids = UserInfo.where("remark like ?", "%#{params[:username]}%").pluck(:username)
        @query_user_list = Kaminari.paginate_array(CustomLdap.query_user_list(uids)).page(params[:page]).per(10)
      end
    else
      @query_user_list = Kaminari.paginate_array(CustomLdap.query_user_list).page(params[:page]).per(10)
    end
  end

  def add_user
    if CustomLdap.add_user(params[:username], params[:password], params[:uid_number], params[:gid_number], params[:home], params[:remark])
      flash[:success] = "添加用户成功"
    else
      flash[:error] = "添加用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def add_account
    username = params[:username]
    `sudo sacctmgr -i add acc name=#{username} cluster=cluster`
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage', notice: "创建账户成功"
  end

  def delete_account
    account_name = params[:account_name]
    `sudo sacctmgr -i del account name=#{account_name}`
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage', notice: "删除账户成功"
  end

  def freeze_user
    if CustomLdap.freeze_user(params[:username])
      flash[:success] = "冻结用户成功"
    else
      flash[:error] = "冻结用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def auth_user
    if CustomLdap.auth_user(params[:username], params[:account_name])
      flash[:success] = "授权用户成功"
    else
      flash[:error] = "授权用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def change_user
    if params[:new_password].blank?
      CustomLdap.change_user(params[:username], nil, params[:remark])
      flash[:success] = "用户修改成功"
    elsif CustomLdap.change_user(params[:username], params[:new_password], params[:remark])
      flash[:success] = "用户修改成功"
    else
      flash[:error] = "修改用户成功，请通知用户"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def delete_user
    if CustomLdap.delete_user(params[:username])
      flash[:success] = "删除用户成功"
    else
      flash[:error] = "删除用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  # 集群管理
  def cluster_manage
  end

  # 集群管理-左侧
  def cluster_left
    output = `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh show`
    # 将输出结果按行分割成数组
    lines = output.split("\n")

    nodes = []
    # 遍历每一行并将每一行的字段分割成数组
    lines.each do |line|
      fields = line.split(" ")
      name = fields[0]
      ip = fields[1]
      bmc = fields[2]
      state = fields[3]
      nodes << {name: name, ip: ip, bmc: bmc, state: state}
    end
    @nodes = Kaminari.paginate_array(nodes).page(params[:page]).per(10)
  end

  # 集群管理-右侧
  def cluster_right
    output = `sinfo`
    # 将输出结果按行分割成数组
    lines = output.split("\n")

    nodes = []
    # 遍历每一行并将每一行的字段分割成数组
    lines.each_with_index do |line, index|
      next if index == 0

      fields = line.split(" ")
      name = fields[0]
      avail = fields[1]
      time_limit = fields[2]
      nodes_arr = fields[3]
      state = fields[4]
      node_list = fields[5]
      nodes << {name: name, avail: avail, time_limit: time_limit, nodes_arr: nodes_arr, state: state, node_list: node_list}
    end
    @nodes = Kaminari.paginate_array(nodes).page(params[:page]).per(10)
  end

  def change_node
    return render json: {message: '请选择节点！'} if params[:nodes].blank?
    return render json: {message: '请选择操作！'} if params[:state].blank?
    message_arr = []
    error_arr = []
    params[:nodes].each do |node|
      output = case params[:state]
                when "start"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh start #{node}`
                when "stop"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh stop #{node}`
                when "reset"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh reset #{node}`
                when "uid"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh uid #{node}`
                end
      if output&.include?("ERROR")
        error_arr << "#{node}: #{output}"
      else
        message_arr << "#{node}: #{output}"
      end
    end
    status = 'failed'
    if error_arr.blank?
      message = '操作成功！'
      status = 'success'
    else
      success_message = message_arr.join(", ")
      fail_message = error_arr.join(", ")
      message = if success_message.blank?
        "操作失败：#{fail_message}"
      else
        "操作成功：#{success_message}，操作失败：#{fail_message}"
      end
    end

    render json: {message: message, status: status}
  end

  # 账单管理
  def user_bills
    if params[:start_time].present?
      tmp_start_time = Time.parse(params[:start_time]).strftime('%F-%H:%M')
    else
      tmp_start_time = (Time.now - 1.hour).strftime('%F-%H:%M')
    end

    if params[:end_time].present?
      tmp_end_time = Time.parse(params[:end_time]).strftime('%F-%H:%M')
    else
      tmp_end_time = Time.now.strftime('%F-%H:%M')
    end

    @total_jishi = CustomLdap.query_raw(tmp_start_time, tmp_end_time, "elapsedRAW", params[:username])
    @total_heshi = CustomLdap.query_raw(tmp_start_time, tmp_end_time, "cputimeRAW", params[:username])
    @total_kashi = CustomLdap.query_raw(tmp_start_time, tmp_end_time, "elapsedRAW,alloctres", params[:username])
    # output = `sacct -S 2022-01-01 -E #{Time.now.to_date.to_s} --format=account,jobid,jobname,state,partition,nodelist,elapsed,cputime,alloctres --allusers --parsable2 --noheader`

    output = `sacct -S #{tmp_start_time} -E #{tmp_end_time} --format=account,user,jobname,partition,elapsedRAW,cputimeRAW,start,end,alloctres,stat --allusers --parsable2 --noheader |grep -v batch`

    # 将输出结果按行分割成数组
    lines = output.split("\n")

    bills = []
    # 遍历每一行并将每一行的字段分割成数组
    lines.each do |line|
      fields = line.split("|")
      account = fields[0]
      user = fields[1]
      jobname = fields[2]
      partition = fields[3]
      elapsed_raw = (fields[4].to_i / 3600.0).round(2)
      cputime_raw = (fields[5].to_i / 3600.0).round(2)
      start_time = fields[6]
      end_time = fields[7]
      alloctres = fields[8]
      stat = fields[9]
      bills << {account: account, user: user, jobname: jobname, partition: partition, elapsed_raw: elapsed_raw, cputime_raw: cputime_raw, start_time: start_time, end_time: end_time, alloctres: alloctres, stat: stat}
    end
    @bills = if params[:label].present? && params[:username].present?
                if params[:label] == "用户账号"
                  @total_bills = search_results([params[:username]], bills)
                  Kaminari.paginate_array(search_results([params[:username]], bills))
                else
                  uids = UserInfo.where("remark like ?", "%#{params[:username]}%").pluck(:username)
                  @total_bills = search_results(uids, bills)
                  Kaminari.paginate_array(search_results(uids, bills))
                end
              else
                @total_bills = bills
                Kaminari.paginate_array(bills)
              end
    @bills_count =  @total_bills.size
    @all_bills = @bills
    @bills = @bills.page(params[:page]).per(10)
    if params[:download].present?
      CSV.open(Rails.root.join("tmp/用户账单.csv"), 'w', encoding: 'UTF-8') do |csv|
        csv << ["\xEF\xBB\xBF华信鼎成高性能计算平台记账系统账单（#{Time.now.strftime('%F')}）"]
        csv << ["账户名称：#{params[:username] || '空'}）", "总机时：#{@total_jishi}", "总核时：#{@total_heshi}", "总卡时：#{@total_kashi}"]
        csv << ['账户名称', '用户名', '作业名称', '队列名称', '机时', '核时', '开始时间', '结束时间', '使用资源', '状态']
        @total_bills.each do |bill|
          Rails.logger.error "================="
          Rails.logger.error @total_bills.size
          Rails.logger.error bill
          csv << [bill[:account], bill[:user], bill[:jobname], bill[:partition], bill[:elapsed_raw], bill[:cputime_raw], bill[:start_time], bill[:end_time], bill[:alloctres], bill[:stat]]
        end
      end
      send_file(
        "#{Rails.root}/tmp/用户账单.csv",
        filename: "用户账单.csv",
        type: "application/csv"
      )
    end
  end

  def license_manage
    @mac_address = Cipher.get_mac_address
    @license_list = Cipher.get_license_list
  end

  def license_upload
    return render json: {message: '请选择文件！'} if params[:file].blank?
    begin
      file = params[:file]
      file_name = file.original_filename
      file_path = Rails.root.join('public', 'license', file_name)
      File.open(file_path, 'wb') do |f|
        f.write(file.read)
      end
      success = true
      message = "导入成功！"
    rescue => e
      success = false
      message = "导入失败！#{e.message}"
    end
    render json: {success: success, message: message}
  end

  def license_delete
    return render json: {message: '请选择文件！'} if params[:file_name].blank?
    begin
      file_path = Rails.root.join('public', 'license', params[:file_name])
      File.delete(file_path)
      message = "删除成功！"
    rescue => e
      message = "删除失败！#{e.message}"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/license_manage', notice: message
  end

  private
    def set_layout_container_class
      @layout_container_class = 'container-fluid'
    end

    def search_results(account, arrays)
      results = []
      arrays.each do |array|
        results << array if array[:account].in?(account)
      end
      results
    end

    def check_authorization
      return redirect_to '/pun/sys/dashboard' if @user&.name != 'admin'
    end
end
