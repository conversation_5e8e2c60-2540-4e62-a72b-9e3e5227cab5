class AccountLogsController < ApplicationController
  before_action :set_layout_container_class

  def index
    @page = (params[:page] || 1).to_i
    @per_page = 10

    # 获取账户记录数据
    @total_count = AccountLog.where(username: @user.name).count
    @total_pages = (@total_count.to_f / @per_page).ceil
    @page = [@page, 1].max # 确保页码不小于1
    @page = [@page, @total_pages].min if @total_pages > 0 # 确保页码不大于总页数

    offset = (@page - 1) * @per_page
    @account_logs = AccountLog.where(username: @user.name)
                             .order(created_at: :desc)
                             .limit(@per_page)
                             .offset(offset)

    # 分页信息
    @has_prev = @page > 1
    @has_next = @page < @total_pages
    @prev_page = @page - 1 if @has_prev
    @next_page = @page + 1 if @has_next
  end

  private

  def set_layout_container_class
    @layout_container_class = 'container-md'
  end
end
