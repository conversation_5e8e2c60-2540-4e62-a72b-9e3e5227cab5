class BillingSettingsController < ApplicationController
  before_action :set_layout_container_class

  def index
    @machine_time_change = MachineTimeChange.first || MachineTimeChange.new
    @recent_billing_logs = AccountLog.where("title LIKE ?", "%计算资源使用费%")
                                   .order(created_at: :desc)
                                   .limit(20)
  end

  def update
    @machine_time_change = MachineTimeChange.first || MachineTimeChange.new
    
    if @machine_time_change.update(machine_time_change_params)
      flash[:success] = "费率配置更新成功"
    else
      flash[:error] = "费率配置更新失败: #{@machine_time_change.errors.full_messages.join(', ')}"
    end
    
    redirect_to billing_settings_path
  end

  def run_billing
    begin
      # 在后台执行扣费任务
      system("cd #{Rails.root} && rake billing RAILS_ENV=#{Rails.env} &")
      flash[:success] = "扣费任务已启动，请查看日志了解执行情况"
    rescue => e
      flash[:error] = "启动扣费任务失败: #{e.message}"
    end
    
    redirect_to billing_settings_path
  end

  private

  def set_layout_container_class
    @layout_container_class = 'container-md'
  end

  def machine_time_change_params
    params.require(:machine_time_change).permit(:cpu, :gpu, :ram)
  end
end
