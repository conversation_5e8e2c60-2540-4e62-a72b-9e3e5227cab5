class DashboardController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:update_password, :upload_avatar]

  def index
    render layout: "dashboard/index"

    begin
      @motd = MotdFile.new.formatter
    rescue StandardError => e
      flash.now[:alert] = t('dashboard.motd_erb_render_error', error_message: e.message)
    end
    set_my_quotas
  end

  def logout
  end

  def change_password
  end

  def update_password
    # Rails.logger.info "====================="
    # Rails.logger.info `ldappasswd -x -h ************* -p 389 -D "cn=Manager,dc=hpc,dc=com" -w Password@_ "cn=#{@user.name},ou=People,dc=hpc,dc=com" -a #{params[:old_password]} -s #{params[:password]}`
    tmp_info = `ldapsearch -x -H ldap://mgt01:389 -b dc=hpc,dc=com -D "cn=Manager,dc=hpc,dc=com" -w Password@_  |grep -w #{@user.name}  |grep dn  | tail -n 1  | awk -F"ou" 'sub($1,"")'`
    result = `ldappasswd -x -h mgt01 -p 389 -D "cn=Manager,dc=hpc,dc=com" -w Password@_ "cn=#{@user.name},#{tmp_info}" -a #{params[:old_password]} -s #{params[:password]}`.include?("old password")
    # result = `ldappasswd -x -h mgt01 -p 389 -D "cn=Manager,dc=hpc,dc=com" -w Password@_ "cn=#{@user.name},ou=People,dc=hpc,dc=com" -a #{params[:old_password]} -s #{params[:password]}`.include?("old password")
    if result
      redirect_to user_change_password_path, notice: '旧密码不正确'
    else
      redirect_to user_change_password_path, notice: '密码更新成功'
    end
  end

  def upload_avatar
    if params[:avatar].present?
      begin
        uploaded_file = params[:avatar]

        # 验证文件类型
        unless uploaded_file.content_type.in?(['image/jpeg', 'image/jpg', 'image/png', 'image/gif'])
          render json: { success: false, message: '只支持 JPG、PNG、GIF 格式的图片' }
          return
        end

        # 验证文件大小 (限制为2MB)
        if uploaded_file.size > 2.megabytes
          render json: { success: false, message: '图片大小不能超过2MB' }
          return
        end

        # 生成唯一文件名
        file_extension = File.extname(uploaded_file.original_filename)
        filename = "#{@user.name}_#{Time.current.to_i}#{file_extension}"
        file_path = Rails.root.join('public', 'avatars', filename)

        # 保存文件
        File.open(file_path, 'wb') do |file|
          file.write(uploaded_file.read)
        end

        # 更新用户信息
        user_info = UserInfo.find_or_create_by(username: @user.name)

        # 删除旧头像文件（如果存在且不是默认头像）
        if user_info.avatar.present?
          old_file_path = Rails.root.join('public', 'avatars', user_info.avatar)
          File.delete(old_file_path) if File.exist?(old_file_path)
        end

        user_info.update(avatar: filename)

        render json: {
          success: true,
          message: '头像上传成功',
          avatar_url: user_info.avatar_url
        }
      rescue => e
        render json: { success: false, message: "上传失败: #{e.message}" }
      end
    else
      render json: { success: false, message: '请选择要上传的图片' }
    end
  end
end
