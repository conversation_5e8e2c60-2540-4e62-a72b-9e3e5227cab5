require 'csv'
class AdminDashboardController < ApplicationController
  before_action :check_authorization
  skip_before_action :verify_authenticity_token, only: [:license_upload, :license_delete]
  before_action :set_layout_container_class, only: [:statistics, :user_manage, :cluster_manage, :user_bills, :license_manage]

  def index
    render layout: "admin/index"
  end

  # 集群资源统计
  def statistics
    @idle_nodes_count, @drain_nodes_count, @down_nodes_count = UsageType.single_usage
    @cpu_usage, @cpu_usage_rencent, @memory_usage, @memory_usage_rencent, @available_storage_usage, @available_storage_rencent, @title_arr = UsageType.total_usage
  end

  # 用户管理
  def user_manage
    if params[:label].present? && params[:username].present?
      if params[:label] == "用户账号"
        @query_user_list = Kaminari.paginate_array(CustomLdap.query_user_list(params[:username])).page(params[:page]).per(10)
      else
        uids = UserInfo.where("remark like ?", "%#{params[:username]}%").pluck(:username)
        @query_user_list = Kaminari.paginate_array(CustomLdap.query_user_list(uids)).page(params[:page]).per(10)
      end
    else
      @query_user_list = Kaminari.paginate_array(CustomLdap.query_user_list).page(params[:page]).per(10)
    end
  end

  def add_user
    if CustomLdap.add_user(params[:username], params[:password], params[:uid_number], params[:gid_number], params[:home], params[:remark])
      flash[:success] = "添加用户成功"
    else
      flash[:error] = "添加用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def freeze_user
    if CustomLdap.freeze_user(params[:username])
      flash[:success] = "冻结用户成功"
    else
      flash[:error] = "冻结用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def auth_user
    if CustomLdap.auth_user(params[:username])
      flash[:success] = "授权用户成功"
    else
      flash[:error] = "授权用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def change_user
    if params[:new_password].blank?
      CustomLdap.change_user(params[:username], nil, params[:remark])
      flash[:success] = "用户修改成功"
    elsif CustomLdap.change_user(params[:username], params[:new_password], params[:remark])
      flash[:success] = "用户修改成功"
    else
      flash[:error] = "修改用户成功，请通知用户"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  def delete_user
    if CustomLdap.delete_user(params[:username])
      flash[:success] = "删除用户成功"
    else
      flash[:error] = "删除用户失败，请联系管理员"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/user_manage'
  end

  # 集群管理
  def cluster_manage
  end

  # 集群管理-左侧
  def cluster_left
    output = `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh show`
    # 将输出结果按行分割成数组
    lines = output.split("\n")

    nodes = []
    # 遍历每一行并将每一行的字段分割成数组
    lines.each do |line|
      fields = line.split(" ")
      name = fields[0]
      ip = fields[1]
      bmc = fields[2]
      state = fields[3]
      nodes << {name: name, ip: ip, bmc: bmc, state: state}
    end
    @nodes = Kaminari.paginate_array(nodes).page(params[:page]).per(10)
  end

  # 集群管理-右侧
  def cluster_right
    output = `sinfo`
    # 将输出结果按行分割成数组
    lines = output.split("\n")

    nodes = []
    # 遍历每一行并将每一行的字段分割成数组
    lines.each_with_index do |line, index|
      next if index == 0

      fields = line.split(" ")
      name = fields[0]
      avail = fields[1]
      time_limit = fields[2]
      nodes_arr = fields[3]
      state = fields[4]
      node_list = fields[5]
      nodes << {name: name, avail: avail, time_limit: time_limit, nodes_arr: nodes_arr, state: state, node_list: node_list}
    end
    @nodes = Kaminari.paginate_array(nodes).page(params[:page]).per(10)
  end

  def change_node
    return render json: {message: '请选择节点！'} if params[:nodes].blank?
    return render json: {message: '请选择操作！'} if params[:state].blank?
    message_arr = []
    error_arr = []
    params[:nodes].each do |node|
      output = case params[:state]
                when "start"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh start #{node}`
                when "stop"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh stop #{node}`
                when "reset"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh reset #{node}`
                when "uid"
                  `cd /usr/share/sxcloud/bin && ./thrive_BMC_config.sh uid #{node}`
                end
      if output&.include?("ERROR")
        error_arr << "#{node}: #{output}"
      else
        message_arr << "#{node}: #{output}"
      end
    end
    status = 'failed'
    if error_arr.blank?
      message = '操作成功！'
      status = 'success'
    else
      success_message = message_arr.join(", ")
      fail_message = error_arr.join(", ")
      message = if success_message.blank?
        "操作失败：#{fail_message}"
      else
        "操作成功：#{success_message}，操作失败：#{fail_message}"
      end
    end

    render json: {message: message, status: status}
  end

  # 账单管理
  def user_bills
    output = `sacct -S 2022-01-01 -E #{Time.now.to_date.to_s} --format=account,jobid,jobname,state,partition,nodelist,elapsedRAW,cputimeRAW,alloctres --allusers --parsable2 --noheader |grep -v batch`

    # 将输出结果按行分割成数组
    lines = output.split("\n")

    bills = []
    # 遍历每一行并将每一行的字段分割成数组
    lines.each do |line|
      fields = line.split("|")
      account = fields[0]
      job_id = fields[1]
      job_name = fields[2]
      state = fields[3]
      partition = fields[4]
      nodelist = fields[5]
      elapsed = fields[6]
      cputime = fields[7]
      alloctres = fields[8]
      bills << {account: account, job_id: job_id, job_name: job_name, state: state, partition: partition, nodelist: nodelist, elapsed: elapsed, cputime: cputime, alloctres: alloctres}
    end
    @bills = if params[:label].present? && params[:username].present?
                if params[:label] == "用户账号"
                  Kaminari.paginate_array(search_results([params[:username]], bills))
                else
                  uids = UserInfo.where("remark like ?", "%#{params[:username]}%").pluck(:username)
                  Kaminari.paginate_array(search_results(uids, bills))
                end
              else
                Kaminari.paginate_array(bills)
              end
    @all_bills = @bills
    @bills = @bills.page(params[:page]).per(10)
    if params[:download].present?
      CSV.open(Rails.root.join("tmp/用户账单.csv"), 'w') do |csv|
        csv << ['用户账号', 'JobID', 'JobName', 'Status', 'Partition', 'Nodelist', 'Elapsed', 'CPUTime', 'AllocTRES']
        @all_bills.each do |bill|
          csv << [bill[:account], bill[:job_id], bill[:job_name], bill[:state], bill[:partition], bill[:nodelist], bill[:elapsed], bill[:cputime], bill[:alloctres]]
        end
      end
      send_file(
        "#{Rails.root}/tmp/用户账单.csv",
        filename: "用户账单.csv",
        type: "application/csv"
      )
    end
  end

  def license_manage
    @mac_address = Cipher.get_mac_address
    @license_list = Cipher.get_license_list
  end

  def license_upload
    return render json: {message: '请选择文件！'} if params[:file].blank?
    begin
      file = params[:file]
      file_name = file.original_filename
      file_path = Rails.root.join('public', 'license', file_name)
      File.open(file_path, 'wb') do |f|
        f.write(file.read)
      end
      success = true
      message = "导入成功！"
    rescue => e
      success = false
      message = "导入失败！#{e.message}"
    end
    render json: {success: success, message: message}
  end

  def license_delete
    return render json: {message: '请选择文件！'} if params[:file_name].blank?
    begin
      file_path = Rails.root.join('public', 'license', params[:file_name])
      File.delete(file_path)
      message = "删除成功！"
    rescue => e
      message = "删除失败！#{e.message}"
    end
    redirect_to '/pun/sys/dashboard/admin_dashboard/license_manage', notice: message
  end

  private
    def set_layout_container_class
      @layout_container_class = 'container-fluid'
    end

    def search_results(account, arrays)
      results = []
      arrays.each do |array|
        results << array if array[:account].in?(account)
      end
      results
    end

    def check_authorization
      return redirect_to '/pun/sys/dashboard' if @user&.name != 'admin'
    end
end
