= paginator.render do
  nav
    ul.pagination
      == first_page_tag unless current_page.first?
      == prev_page_tag unless current_page.first?
      - each_page do |page|
        - if page.left_outer? || page.right_outer? || page.inside_window?
          == page_tag page
        - elsif !page.was_truncated?
          == gap_tag
      == next_page_tag unless current_page.last?
      == last_page_tag unless current_page.last?
