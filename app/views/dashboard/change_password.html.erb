<% content_for :content do %>
  <style>
    .content-body {
      height: calc(100vh - 100px);
      background: white;
      border-radius: 16px;
      padding: 22px 30px;
    }

    .avatar-container {
      text-align: center;
    }
    .avatar-preview {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      border: 3px solid #e0e0e0;
      margin-bottom: 15px;
      cursor: pointer;
    }
    .avatar-upload-btn {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      color: #6c757d;
    }
    .avatar-upload-btn:hover {
      background: #e9ecef;
    }
    #avatar-file-input {
      display: none;
    }
  </style>
  <div class="header" style="display: flex;justify-content: space-between;">
    <div><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
    <div style="display: flex;align-items:center;">
      <div><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></div>
      <div><a class="logout-text" href="/logout"><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></a></div>
    </div>
  </div>
  <div class="content-body">
    <a class="logout-text" href="/pun/sys/dashboard">
      <%= image_tag "/new/home/<USER>", style: "width: 30px;height: 30px;" %>
    </a>
    <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
      <div style="width: 1280px;display: flex;justify-content:center;">
        <div style="width: 480px;">
          <div style="padding-top: 80px;">
            <!-- 头像上传区域 -->
            <div class="avatar-container">
              <% user_info = UserInfo.find_by(username: @user.name) %>
              <img id="avatar-preview" src="/pun/sys/dashboard<%= user_info&.avatar_url || '/avatars/default.svg' %>"
                   alt="用户头像" class="avatar-preview" onclick="document.getElementById('avatar-file-input').click();">
              <br>
              <input type="file" id="avatar-file-input" accept="image/*" onchange="uploadAvatar(this)">
              <label for="avatar-file-input" class="avatar-upload-btn" style="display: none;">更换头像</label>
            </div>

            <div style="display: flex;justify-content:center;">
              <div><h1><%= @user.name %></h1></div>
            </div>

            <form action="/pun/sys/dashboard/user/update_password" method="post">
              <label for=""><span style="color: #FF4949">*</span>当前密码：</label>
              <%= password_field_tag :old_password, '', class: "form-control input-placeholder-light", placeholder: "请输入当前密码", required: true %>
              <label for=""><span style="color: #FF4949">*</span>新密码：</label>
              <%= password_field_tag :password, '', class: 'form-control input-placeholder-light', id: 'input1', placeholder: "请输入新密码", required: true %>
              <label for=""><span style="color: #FF4949">*</span>确认密码：</label>
              <%= password_field_tag :password_confirmation, '', class: 'form-control input-placeholder-light', id: 'input2', placeholder: "请再次输入新密码", onblur: "check2pwd()", required: true %>
              <br>
              <div style="color: #FF4949;margin-bottom: 24px;">*忘记密码联系管理员重置</div>
              <button type="submit" class="btn btn-primary btn-block submit-button">提交</button>
            </form>

            <script>
              function check2pwd() {
                if($("#input1").val() != $("#input2").val()) {
                    alert("两次输入密码不一致！")
                    $("#input1").val("");
                    $("#input2").val("");
                }
              }

              function uploadAvatar(input) {
                if (input.files && input.files[0]) {
                  const file = input.files[0];

                  // 验证文件类型
                  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                  if (!allowedTypes.includes(file.type)) {
                    alert('只支持 JPG、PNG、GIF 格式的图片');
                    return;
                  }

                  // 验证文件大小 (2MB)
                  if (file.size > 2 * 1024 * 1024) {
                    alert('图片大小不能超过2MB');
                    return;
                  }

                  // 预览图片
                  const reader = new FileReader();
                  reader.onload = function(e) {
                    document.getElementById('avatar-preview').src = e.target.result;
                  };
                  reader.readAsDataURL(file);

                  // 上传图片
                  const formData = new FormData();
                  formData.append('avatar', file);

                  fetch('/pun/sys/dashboard/user/upload_avatar', {
                    method: 'POST',
                    body: formData,
                    headers: {
                      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                  })
                  .then(response => response.json())
                  .then(data => {
                    if (data.success) {
                      alert(data.message);
                      // 更新头像显示
                      document.getElementById('avatar-preview').src = '/pun/sys/dashboard' + data.avatar_url + '?t=' + new Date().getTime();
                    } else {
                      alert(data.message);
                      // 恢复原来的头像
                      location.reload();
                    }
                  })
                  .catch(error => {
                    console.error('Error:', error);
                    alert('上传失败，请重试');
                    location.reload();
                  });
                }
              }
            </script>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
<%= render 'layouts/dashboard/base' %>