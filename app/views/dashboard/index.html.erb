<%= render partial: 'shared/welcome' %>

<%- dashboard_layout.fetch(:rows, []).each do |row| -%>
<div class="row">
  <%- row.fetch(:columns, []).each do |col| -%>
  <div class='<%= "col-md-#{col[:width]}" %>'> <%# FIXME: what if width is not specified!? %>
    <%- Array(col.fetch(:widgets, [])).each do |widget| -%>
      <%= widget.to_s %>
      <%= render_widget(widget.to_s) %>
    <%- end -%>
  </div>
  <%- end -%>
</div>
<%- end -%>
