<!-- <%= render partial: 'shared/welcome' %>

<%- dashboard_layout.fetch(:rows, []).each do |row| -%>
<div class="row">
  <%- row.fetch(:columns, []).each do |col| -%>
  <div class='<%= "col-md-#{col[:width]}" %>'> <%# FIXME: what if width is not specified!? %>
    <%- Array(col.fetch(:widgets, [])).each do |widget| -%>
      <%= widget.to_s %>
      <%= render_widget(widget.to_s) %>
    <%- end -%>
  </div>
  <%- end -%>
</div>
<%- end -%> -->
<!DOCTYPE html>
<html>

<head>
  <script src="https://cdn.staticfile.org/jquery/1.10.2/jquery.min.js"></script>
  <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">

  <!-- 可选的 Bootstrap 主题文件（一般不用引入） -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap-theme.min.css" integrity="sha384-6pzBo3FDv/PJ8r2KRkGHifhEocL+1X2rVCTTkUfGk7/0pbek5mMa1upzvWbrUbOZ" crossorigin="anonymous">

  <!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
  <style>
    .header {
      background-image: linear-gradient(145deg, #031c37, #07487a);
      color: white;
      height: 80px;
      font-size: 18px;
    }

    .left {
      width: 400px;
      float: left;
      color: #829ebc;
      background-color: #031c37;
      background: #031c37 url('bg.png') no-repeat right bottom;
      height: calc(100vh - 80px);
      padding: 20px;
      overflow: scroll;
    }

    .left .title {
      cursor: pointer;
      margin-top: 20px;
      user-select: none;
      font-size: 20px;
    }

    .left .title img {
      margin-top: -2px;
      margin-left: 2px;
    }

    .body {
      width: 1000px;
      float: left;
      background-color: pink;
      height: calc(100vh - 80px);
      width: calc(100vw - 550px);
    }

    .con {
      display: flex;
      flex-flow: row wrap;
      justify-content: left;
      align-items: center;
    }

    .apps {
      margin-top: 50px;
    }

    .apps-icon {
      margin-top: 20px;
    }

    .apps-icon img {
      width: 48px;
      height: 50px;
      margin-right: 12px;
    }

    .shousuo, .zhankai {
      width: 31px;
      height: 26px;
      float: right;
      cursor: pointer;
    }

    .logo {
      height: 50px;
      margin: 18px 0px 0px 7px;
    }

    .header-right {
      float: right;
      display: flex;
    }

    .header-right .username {
      margin-top: 28px;
      margin-right: 50px;
    }

    .help {
      display: flex;
    }

    .help img {
      width: 32px;
      height: 30px;
      margin: 26px 10px 0px 0px;
      cursor: pointer;
    }

    .help .dropdown {
      color: white;
      cursor: pointer;
      margin-top: 28px;
    }

    .help a {
      color: white;
      cursor: pointer;
    }

    .logout {
      display: flex;
    }

    .logout img {
      width: 32px;
      height: 30px;
      margin: 26px 10px 0px 0px;
      cursor: pointer;
    }

    .logout .logout-text {
      margin: 28px 40px 0px 0px;
      cursor: pointer;
    }

    .split-line {
      margin: 20px 20px 0px 20px;
      border-left: 1px solid #1c5885;
      height: 40px;
    }

    .right {
      width: 150px;
      float: right;
      height: calc(100vh - 80px);
      display: flex;
    }

    .sub-right {
      align-self: center;
      font-size: 18px;
      color: #036bbb;
    }

    .sub-right .module {
      margin-bottom: 40px;
    }

    .sub-right img {
      margin-left: 12px;
      margin-bottom: 5px;
    }

    .home-footer {
      background-color: #d3d3d3;
      height: 56px;
      color: white;
      font-size: 18px;
      text-align: left;
    }

  </style>
</head>

<body>
  <div class="header">
    <img src="logo.png" class="logo">
    <div class="header-right">
      <div class="username">Mark</div>
      <div class="help">
        <img src="help.png">
        <div class="dropdown">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">帮助 <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="#">Action</a></li>
            <li><a href="#">Another action</a></li>
            <li><a href="#">Something else here</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="#">Separated link</a></li>
          </ul>
        </div>
      </div>
      <div class="split-line"></div>
      <div class="logout">
        <img src="logout.png">
        <div class="logout-text">退出</div>
      </div>
    </div>
  </div>

  <div class="container-fiuld">
    <div class="left">
      <div>
        <img src="shousuo.png" class="shousuo">
        <img src="zhankai.png" class="zhankai" style="display: none;">
      </div>
      <div class="apps">
        <div class="title">
          桌面应用
          <img src="xia.png" class="xia">
          <img src="you.png" class="you" style="display: none;">
        </div>
        <div class="con">
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
        </div>
        <div class="title">
          计算应用
          <img src="xia.png" class="xia">
          <img src="you.png" class="you" style="display: none;">
        </div>
        <div class="con">
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
          <div class="apps-icon">
            <img src="./1664702066912.jpg" alt="">
          </div>
        </div>
      </div>
    </div>
    <div class="body">
      <iframe src="https://www.baidu.com" frameborder="0" style="width: 100%;height: calc(100vh - 141px);"></iframe>
      <div class="home-footer">
        <img src="./origin-logo.png" alt="" style="height: 55px;">
        <div style="float: right;margin-top:15px;margin-right: 20px;">2022.10.3</div>
      </div>
    </div>
    <div class="right">
      <div class="sub-right">
        <div class="module">
          <div>
            <img src="right/用户目录.png">
          </div>
          <div>
            用户目录
          </div>
        </div>
        <div class="module">
          <div>
            <img src="right/云台.png">
          </div>
          <div>
            云台桌面
          </div>
        </div>
        <div class="module">
          <div>
            <img src="right/任务.png">
          </div>
          <div>
            任务状态
          </div>
        </div>
        <div class="module">
          <div>
            <img src="right/业务.png">
          </div>
          <div>
            提交业务
          </div>
        </div>
        <div class="module">
          <div>
            <img src="right/终端.png">
          </div>
          <div>
            Shell终端
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    $(".title").click(function(){
      $(this).next().toggle();
      $(this).find('.xia').toggle();
      $(this).find('.you').toggle();

      // 触发展开事件
      $(".zhankai").hide();
      $(".shousuo").show();
      $(".left").css("width", "400px")
    });

    $(".shousuo").click(function(){
      $(".con").hide();
      $(this).hide();
      $(".zhankai").show();
      $(".left").css("width", "144px")
      $(".body").css("width", "calc(100vw - 294px)")
    });

    $(".zhankai").click(function(){
      $(this).hide();
      $(".shousuo").show();
      $(".con").show();
      $(".left").css("width", "400px")
      $(".body").css("width", "calc(100vw - 550px)")
    });

    $(".module").click(function(){
      $("iframe").attr('src', "https://www.google.com");
    });
  </script>
</body>

</html>
