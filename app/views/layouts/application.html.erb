<!DOCTYPE html>
<html lang="<%= Configuration.locale %>">
<head>
  <title><%= content_for?(:title) ? yield(:title) : "超算应用平台|华信鼎成" %></title>
  <%= favicon_link_tag 'favicon.ico', href: OodAppkit.public.url.join('favicon.ico'), skip_pipeline: true %>

  <!-- Webpacker -->
  <%= javascript_pack_tag 'application' %>
  <%= stylesheet_pack_tag 'application', media: 'all' %>

  <!-- (Legacy) Sprockets -->
  <%= stylesheet_link_tag 'application', media: 'all' %>
  <%= javascript_include_tag 'application' %>
  <%= javascript_include_tag 'turbolinks' if Configuration.turbolinks_enabled? %>
  <!-- <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css" integrity="sha384-50oBUHEmvpQ+1lW4y57PTFmhCaXp0ML5d60M1M7uH2+nqUivzIebhndOJK28anvf" crossorigin="anonymous"> -->
  <%#= stylesheet_link_tag '/pun/sys/dashboard/font-awesome-sprockets.css', media: 'all' %>

  <%= csrf_meta_tags %>

  <%= yield :head %>

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <% if Configuration.turbolinks_enabled? %>
    <meta name="turbolinks-root" content="<%= ENV['RAILS_RELATIVE_URL_ROOT'] || "/" %>">
  <% end %>
<%= render partial: '/layouts/nav/styles', locals: { bg_color: Configuration.brand_bg_color, link_active_color: Configuration.brand_link_active_bg_color } %>
</head>
<body>
  <header <%= 'data-turbolinks-permanent' if Configuration.turbolinks_enabled? %>>
    <!-- <nav class="navbar navbar-expand-md shadow-sm navbar-color navbar-<%= Configuration.navbar_type %>" aria-label="Navigation">
    <% if ENV['OOD_DASHBOARD_HEADER_IMG_LOGO'].present? %>
      <a class="navbar-brand navbar-brand-logo" href="<%= root_path %>"><img class="img-fluid" src="<%= ENV['OOD_DASHBOARD_HEADER_IMG_LOGO'] %>"></a>
    <% else %>
      <a class="navbar-brand" href="<%= root_path %>"><%= OodAppkit.dashboard.title.html_safe %></a>
    <% end %>

      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar" aria-controls="navbar" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbar">
        <ul class="navbar-nav mr-auto">
          <%= render partial: 'layouts/nav/featured_apps', locals: { group: @featured_group } if @featured_group.present? %>
          <%= render partial: 'layouts/nav/group', collection: @nav_groups %>
          <%= render partial: 'layouts/nav/sessions', nav_groups: @nav_groups if Configuration.app_development_enabled? || @nav_groups.any?(&:has_batch_connect_apps?) %>
          <%= render partial: 'layouts/nav/all_apps' if Configuration.show_all_apps_link? %>
        </ul>

        <ul class="navbar-nav">
          <%= render partial: 'layouts/nav/develop_dropdown' if Configuration.app_development_enabled? %>
          <%= render partial: "layouts/nav/help_dropdown" %>

          <li class="nav-item" data-container="body" data-toggle="popover" data-content="<%= t('dashboard.nav_user', username: @user.name) %>" data-placement="bottom">
            <a class="nav-link disabled">
              <i class="fas fa-user" aria-hidden="true" title="<%= t('dashboard.nav_user', username: @user.name) %>" aria-hidden="true"></i><span class="d-sm-none d-md-none d-lg-inline"> <%= t('dashboard.nav_user', username: @user.name) %></span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link" href="/logout"><i class="fas fa-sign-out-alt" aria-hidden="true"></i><span class="d-sm-none d-md-none d-lg-inline"> <%= t('dashboard.nav_logout') %></span></a>
          </li>
        </ul>
      </div>
    </nav> -->
  </header>

  <div class="<%= @layout_container_class || local_assigns[:layout_container_class] || 'container-md' %> content mt-4" role="main">

    <% @announcements.select(&:valid?).each do |announcement| %>
      <div class="alert alert-<%= announcement.type %> announcement" role="alert">
        <%= raw OodAppkit.markdown.render(announcement.msg) %>
      </div>
    <% end %>

    <%= render "layouts/browser_warning" %>

    <%= render partial: "shared/insufficient_quota", locals: { quotas: @my_quotas } if @my_quotas && @my_quotas.any? %>
    <%= render partial: "shared/insufficient_balance", locals: { balances: @my_balances } if @my_balances && @my_balances.any? %>
    <%= render partial: "shared/bad_cluster_config" if invalid_clusters.any? %>

    <script type="text/coffee-script-template" id="js-alert-danger-template">
      <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        ALERT_MSG
      </div>
    </script>

    <% if alert %>
      <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <%= alert %>
      </div>
    <% end %>

    <% if notice %>
      <div class="alert alert-success" role="alert">
        <button type="button" class="close" data-dismiss="alert">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <%= notice %>
      </div>
    <% end %>

    <%= yield %>

  </div><!-- /.container -->

  <%= render "layouts/footer" %>
</body>
</html>
