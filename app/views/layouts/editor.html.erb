<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">

  <title><%= "File Editor - #{OodAppkit.dashboard.title}" %><%= @pathname.nil? ? "" : " - #{@pathname.basename.to_s}" %></title>
  <%= favicon_link_tag 'favicon.ico', href: OodAppkit.public.url.join('favicon.ico') %>

  <%= javascript_pack_tag 'application' %>
  <%= stylesheet_pack_tag 'application', media: 'all' %>

  <%= stylesheet_link_tag "editor", media: "all" %>

  <%= javascript_include_tag "editor" %>
  <%= csrf_meta_tags %>
</head>
<body>

<header>
  <!-- navbar  -->
  <nav class="navbar navbar-inverse navbar-static-top" role="navigation">
    <div class="container-fluid">
      <!-- Brand and toggle get grouped for better mobile display -->
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target=".navbar-responsive-collapse">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>

        <ul class="nav navbar-nav">
          <li>
            <button type="button" class="btn btn-primary btn-xs" id="save-button">
              <span class="glyphicon glyphicon-save" id="save-icon" aria-hidden="true"></span> Save
            </button>
          </li>
          <li>
            <span class="label"><%= @pathname %></span><span id="loading-notice" style="display: none;">[ <span class="glyphicon glyphicon-refresh glyphicon-spin" id="loading-notice" aria-hidden="true"></span> Loading ]</span>
          </li>
        </ul>
      </div>

      <!-- Collect the nav links, forms, and other content for toggling -->
      <div class="collapse navbar-collapse navbar-responsive-collapse">
        <ul class="nav navbar-nav navbar-right form-inline">
          <li class="hidden-xs hidden-sm hidden-md">
            <label class="control-label" for="keybindings">Key Bindings</label>
            <div class="form-group">
              <select class="form-control input-xs" id="keybindings">
                <option value="default">Default</option>
                <option value="ace/keyboard/vim">Vim</option>
                <option value="ace/keyboard/emacs">Emacs</option>
              </select>
            </div>
          </li>
          <li class="hidden-xs">
            <label class="control-label hidden-sm hidden-md" for="fontsize">Font Size</label>
            <div class="form-group">
              <select class="form-control input-xs" id="fontsize">
                <option value="8px">8px</option>
                <option value="9px">9px</option>
                <option value="10px">10px</option>
                <option value="11px">11px</option>
                <option value="12px">12px</option>
                <option value="13px">13px</option>
                <option value="14px">14px</option>
                <option value="16px">16px</option>
                <option value="18px">18px</option>
                <option value="20px">20px</option>
                <option value="22px">22px</option>
                <option value="24px">24px</option>
                <option value="26px">26px</option>
                <option value="28px">28px</option>
                <option value="36px">36px</option>
                <option value="48px">48px</option>
                <option value="72px">72px</option>
              </select>
            </div>
          </li>
          <li class="hidden-xs">
            <label class="control-label hidden-sm hidden-md" for="mode">Mode</label>
            <div class="form-group">
              <select class="form-control input-xs" id="mode">
                <option value="abap">ABAP</option>
                <option value="abc">ABC</option>
                <option value="actionscript">ActionScript</option>
                <option value="ada">ADA</option>
                <option value="apache_conf">Apache Conf</option>
                <option value="asciidoc">AsciiDoc</option>
                <option value="assembly_x86">Assembly x86</option>
                <option value="autohotkey">AutoHotKey</option>
                <option value="batchfile">BatchFile</option>
                <option value="c_cpp">C and C++</option>
                <option value="c9search">C9Search</option>
                <option value="cirru">Cirru</option>
                <option value="clojure">Clojure</option>
                <option value="cobol">Cobol</option>
                <option value="coffee">CoffeeScript</option>
                <option value="coldfusion">ColdFusion</option>
                <option value="csharp">C#</option>
                <option value="css">CSS</option>
                <option value="curly">Curly</option>
                <option value="d">D</option>
                <option value="dart">Dart</option>
                <option value="diff">Diff</option>
                <option value="dockerfile">Dockerfile</option>
                <option value="dot">Dot</option>
                <option value="eiffel">Eiffel</option>
                <option value="ejs">EJS</option>
                <option value="elixir">Elixir</option>
                <option value="elm">Elm</option>
                <option value="erlang">Erlang</option>
                <option value="forth">Forth</option>
                <option value="ftl">FreeMarker</option>
                <option value="gcode">Gcode</option>
                <option value="gherkin">Gherkin</option>
                <option value="gitignore">Gitignore</option>
                <option value="glsl">Glsl</option>
                <option value="gobstones">Gobstones</option>
                <option value="golang">Go</option>
                <option value="groovy">Groovy</option>
                <option value="haml">HAML</option>
                <option value="handlebars">Handlebars</option>
                <option value="haskell">Haskell</option>
                <option value="haxe">haXe</option>
                <option value="html">HTML</option>
                <option value="html_elixir">HTML (Elixir)</option>
                <option value="html_ruby">HTML (Ruby)</option>
                <option value="ini">INI</option>
                <option value="io">Io</option>
                <option value="jack">Jack</option>
                <option value="jade">Jade</option>
                <option value="java">Java</option>
                <option value="javascript">JavaScript</option>
                <option value="json">JSON</option>
                <option value="jsoniq">JSONiq</option>
                <option value="jsp">JSP</option>
                <option value="jsx">JSX</option>
                <option value="julia">Julia</option>
                <option value="latex">LaTeX</option>
                <option value="lean">Lean</option>
                <option value="less">LESS</option>
                <option value="liquid">Liquid</option>
                <option value="lisp">Lisp</option>
                <option value="livescript">LiveScript</option>
                <option value="logiql">LogiQL</option>
                <option value="lsl">LSL</option>
                <option value="lua">Lua</option>
                <option value="luapage">LuaPage</option>
                <option value="lucene">Lucene</option>
                <option value="makefile">Makefile</option>
                <option value="markdown">Markdown</option>
                <option value="mask">Mask</option>
                <option value="matlab">MATLAB</option>
                <option value="maze">Maze</option>
                <option value="mel">MEL</option>
                <option value="mushcode">MUSHCode</option>
                <option value="mysql">MySQL</option>
                <option value="nix">Nix</option>
                <option value="nsis">NSIS</option>
                <option value="objectivec">Objective-C</option>
                <option value="ocaml">OCaml</option>
                <option value="pascal">Pascal</option>
                <option value="perl">Perl</option>
                <option value="pgsql">pgSQL</option>
                <option value="php">PHP</option>
                <option value="powershell">Powershell</option>
                <option value="praat">Praat</option>
                <option value="prolog">Prolog</option>
                <option value="properties">Properties</option>
                <option value="protobuf">Protobuf</option>
                <option value="python">Python</option>
                <option value="r">R</option>
                <option value="razor">Razor</option>
                <option value="rdoc">RDoc</option>
                <option value="rhtml">RHTML</option>
                <option value="rst">RST</option>
                <option value="ruby">Ruby</option>
                <option value="rust">Rust</option>
                <option value="sass">SASS</option>
                <option value="scad">SCAD</option>
                <option value="scala">Scala</option>
                <option value="scheme">Scheme</option>
                <option value="scss">SCSS</option>
                <option value="sh">SH</option>
                <option value="sjs">SJS</option>
                <option value="smarty">Smarty</option>
                <option value="snippets">snippets</option>
                <option value="soy_template">Soy Template</option>
                <option value="space">Space</option>
                <option value="sql">SQL</option>
                <option value="sqlserver">SQLServer</option>
                <option value="stylus">Stylus</option>
                <option value="svg">SVG</option>
                <option value="swift">Swift</option>
                <option value="tcl">Tcl</option>
                <option value="tex">Tex</option>
                <option value="text">Text</option>
                <option value="textile">Textile</option>
                <option value="toml">Toml</option>
                <option value="twig">Twig</option>
                <option value="typescript">Typescript</option>
                <option value="vala">Vala</option>
                <option value="vbscript">VBScript</option>
                <option value="velocity">Velocity</option>
                <option value="verilog">Verilog</option>
                <option value="vhdl">VHDL</option>
                <option value="wollok">Wollok</option>
                <option value="xml">XML</option>
                <option value="xquery">XQuery</option>
                <option value="yaml">YAML</option>
                <option value="django">Django</option>
              </select>
            </div>
          </li>
          <li class="hidden-xs">
            <label class="control-label hidden-sm hidden-md" for="theme">Theme</label>
            <div class="form-group">
              <select class="form-control input-xs" id="theme">
                <optgroup label="Bright">
                  <option value="ace/theme/chrome">Chrome</option>
                  <option value="ace/theme/clouds">Clouds</option>
                  <option value="ace/theme/crimson_editor">Crimson Editor</option>
                  <option value="ace/theme/dawn">Dawn</option>
                  <option value="ace/theme/dreamweaver">Dreamweaver</option>
                  <option value="ace/theme/eclipse">Eclipse</option>
                  <option value="ace/theme/github">GitHub</option>
                  <option value="ace/theme/iplastic">IPlastic</option>
                  <option value="ace/theme/solarized_light">Solarized Light</option>
                  <option value="ace/theme/textmate">TextMate</option>
                  <option value="ace/theme/tomorrow">Tomorrow</option>
                  <option value="ace/theme/xcode">XCode</option>
                  <option value="ace/theme/kuroir">Kuroir</option>
                  <option value="ace/theme/katzenmilch">KatzenMilch</option>
                  <option value="ace/theme/sqlserver">SQL Server</option>
                </optgroup>
                <optgroup label="Dark">
                  <option value="ace/theme/ambiance">Ambiance</option>
                  <option value="ace/theme/chaos">Chaos</option>
                  <option value="ace/theme/clouds_midnight">Clouds Midnight</option>
                  <option value="ace/theme/cobalt">Cobalt</option>
                  <option value="ace/theme/idle_fingers">idle Fingers</option>
                  <option value="ace/theme/kr_theme">krTheme</option>
                  <option value="ace/theme/merbivore">Merbivore</option>
                  <option value="ace/theme/merbivore_soft">Merbivore Soft</option>
                  <option value="ace/theme/mono_industrial">Mono Industrial</option>
                  <option value="ace/theme/monokai">Monokai</option>
                  <option value="ace/theme/pastel_on_dark">Pastel on dark</option>
                  <option value="ace/theme/solarized_dark">Solarized Dark</option>
                  <option value="ace/theme/terminal">Terminal</option>
                  <option value="ace/theme/tomorrow_night">Tomorrow Night</option>
                  <option value="ace/theme/tomorrow_night_blue">Tomorrow Night Blue</option>
                  <option value="ace/theme/tomorrow_night_bright">Tomorrow Night Bright</option>
                  <option value="ace/theme/tomorrow_night_eighties">Tomorrow Night 80s</option>
                  <option value="ace/theme/twilight">Twilight</option>
                  <option value="ace/theme/vibrant_ink">Vibrant Ink</option>
                </optgroup>
              </select>
            </div>
          </li>
          <li class="hidden-xs">
            <label class="control-label hidden-sm" for="wordwrap">Word Wrap</label>
            <label class="control-label hidden-md hidden-lg" for="wordwrap">Wrap</label>
            <div class="form-group">
              <input type="checkbox" id="wordwrap" value="">
            </div>
          </li>
        </ul>
      </div><!-- /.navbar-collapse -->
    </div><!-- /.container-fluid -->
  </nav>
</header>

<div class="container" role="main">

  <% if alert %>
      <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <%= alert %>
      </div>
  <% end %>

  <% if notice %>
      <div class="alert alert-success" role="alert">
        <button type="button" class="close" data-dismiss="alert">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <%= notice %>
      </div>
  <% end %>

  <%= yield %>

</div><!-- /.container -->

<footer>
  <div class="container">
    <hr>
    <p>&copy; Ohio Supercomputer Center <%= DateTime.now.year %></p>
  </div>
</footer>

</body>
</html>
