<li class="nav-item dropdown" title="<%= group.title %>" >
  <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><%= group.title %><span class="caret"></span></a>

  <ul class="dropdown-menu">
    <% OodAppGroup.groups_for(apps: group.apps, group_by: :subcategory, nav_limit: group.nav_limit).each_with_index do |g, index| %>
      <%= content_tag "li", "", class: "dropdown-divider", role: "separator" if index > 0 %>
      <%= content_tag "li", g.title_with_nav_limit_caption, class: "dropdown-header" unless g.title_with_nav_limit_caption.empty? %>
      <% g.apps.take(g.nav_limit).map { |app| app.links.first }.compact.each do |link| %>
          <li>
            <%=
              link_to(
                link.url.to_s,
                title: link.title,
                class: "dropdown-item",
                target: link.new_tab? ? "_blank" : nil
              ) do
            %>
              <%= icon_tag(link.icon_uri) %> <%=link.title %>
              <% if link.subtitle.present? %>
                <%= content_tag(:small, link.subtitle, style: "text-indent: 20px", class: 'visible-xs-block visible-sm-inline visible-md-inline visible-lg-inline') %>
              <% end %>
            <% end %>
          </li>
      <% end %>
    <% end %>

    <%= content_tag "li", "", class: "dropdown-divider", role: "separator" %>
    <li title="<%= t('dashboard.nav_all_apps') %>">
      <a href="<%= apps_index_path %>" class="dropdown-item">
        <i class="fas fa-th" aria-hidden="true"></i> <%= t('dashboard.nav_all_apps') %>
      </a>
    </li>
  </ul>
</li>
