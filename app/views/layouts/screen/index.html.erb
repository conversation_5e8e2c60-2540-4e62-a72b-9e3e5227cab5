<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>可视化大屏</title>
</head>
<body>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: rgba(7, 16, 49, 1);
      overflow: auto; /* 或 scroll */
      scrollbar-width: none;  /* Firefox */
      -ms-overflow-style: none;  /* IE 和 Edge */
    }

    body::-webkit-scrollbar {
      display: none;
    }

    .main {
      height: 100vh;
      width: 100vw;
      background-color: rgba(7, 16, 49, 1);
      overflow: auto; /* 或 scroll */
      scrollbar-width: none;  /* Firefox */
      -ms-overflow-style: none;  /* IE 和 Edge */
    }

    .main::-webkit-scrollbar {
      display: none;
    }

    .header {
      display: flex;
      font-size: 30px;
    }

    .content {
      display: flex;
      gap: 20px;
    }

    .rectangle-left {
      font-size: 16px;
      width: 25%;
      height: 50px;
      background: #0079fe;
      display: flex;
      justify-content: left;
      align-items: center;
      color: white;
      font-weight: bold;
      padding-left: 20px;
      clip-path: polygon(
        0 0,
        calc(100% - 50px) 0,
        100% 50px,
        100% 100%,
        0 100%
      );
    }

    .title {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #FFFFFF;
      opacity: 0.7;
      width: 50%;
      font-weight: bold;
    }

    .rectangle-right {
      font-size: 16px;
      width: 25%;
      height: 50px;
      background: #0079fe;
      display: flex;
      justify-content: right;
      padding-right: 20px;
      align-items: center;
      color: white;
      font-weight: bold;
      clip-path: polygon(
        50px 0,
        100% 0,
        100% 100%,
        0 100%,
        0 50px
      );
    }

    .content-left-title {
      font-size: 20px;
      color: #FFFFFF;
      font-weight: bold;
    }

    .icon {
      height: 20px;
      width: 6px;
      background-color: rgba(193, 170, 108, 1);
      border-radius: 3px;
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .module {
      border-width: 0px;
      width: 295px;
      height: 133px;
      background: inherit;
      background-color: rgba(30, 44, 71, 1);
      box-sizing: border-box;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(50, 68, 100, 1);
      border-radius: 0px;
      -moz-box-shadow: none;
      -webkit-box-shadow: none;
      box-shadow: none;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;
      padding: 20px;
    }

    .module-title {
      font-size: 14px;
      color: #98D8FC;
      font-weight: 400;
    }

    .module-number {
      font-weight: 700;
      font-size: 34px;
      color: #FFFFFF;
    }

    .module-unit {
      font-size: 18px;
      color: white;
      font-weight: 650;
    }

    .module-description {
      font-size: 14px;
      color: #98D8FC;
      font-weight: 100;
    }

    .content-title {
      display: flex;
      align-items: center;
    }

    .content-title-text {
      font-size: 20px;
      color: #FFFFFF;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .circle-area {
      text-align: center;
      color: #98D8FC;
    }

    .node-number {
      background-color: rgba(218, 200, 68, 0.2980392156862745);
      padding: 2px 4px;
      font-weight: 700;
      font-size: 34px;
    }
    .node-box {
      background-color: rgba(30, 44, 71, 1);
      height: 62px;
      border-color: rgba(50, 68, 100, 1);
      border-width: 1px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 3px;
      margin-top: 10px;
    }
    .progress-container {
      width: 80px;
      max-width: 600px;
      height: 5px;
      background: #222;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 0 10px #0ff4, 0 0 30px #0ff4;
      border: 2px solid #0ff;
    }

    .progress-bar {
      height: 100%;
      width: 70%; /* 控制进度条的进度 */
      background: linear-gradient(270deg, #0ff, #00f, #0ff);
      background-size: 400% 400%;
      animation: gradientMove 4s ease infinite;
      border-radius: 20px 0 0 20px;
      box-shadow: 0 0 15px #0ff;
    }

    @keyframes gradientMove {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
  </style>
  <div class="main">
    <div class="header" style="zoom: 1.7;">
      <div class="rectangle-left">
        数据监测可视化大屏
        <svg width="200px" height="50px" style="margin-left: 10px;">
          <rect fill="#fff" x="8.273809523809524" y="8.75" width="2.5" height="2.5"><!----></rect>
          <rect fill="#fff" x="17.797619047619047" y="8.75" width="2.5" height="2.5"><!----></rect><!---->
          <rect fill="#fff" x="36.845238095238095" y="8.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.8498411564592054"
              repeatCount="indefinite"></animate>
          </rect><!----><!----><!----><!---->
          <rect fill="#fff" x="84.46428571428571" y="8.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.7772305561371131"
              repeatCount="indefinite"></animate>
          </rect><!----><!---->
          <rect fill="#fff" x="113.03571428571428" y="8.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.6225565445503529"
              repeatCount="indefinite"></animate>
          </rect><!----><!---->
          <rect fill="#fff" x="141.60714285714286" y="8.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.3406653530754802"
              repeatCount="indefinite"></animate>
          </rect><!----><!----><!----><!---->
          <rect fill="#fff" x="189.22619047619048" y="8.75" width="2.5" height="2.5"><!----></rect>
          <rect fill="#fff" x="8.273809523809524" y="18.75" width="2.5" height="2.5"><!----></rect>
          <!----><!----><!----><!---->
          <rect fill="#fff" x="55.89285714285714" y="18.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.1118490343419587"
              repeatCount="indefinite"></animate>
          </rect><!---->
          <rect fill="#fff" x="74.94047619047619" y="18.75" width="2.5" height="2.5"><!----></rect>
          <rect fill="#fff" x="84.46428571428571" y="18.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.15996524011086"
              repeatCount="indefinite"></animate>
          </rect><!----><!---->
          <rect fill="#fff" x="113.03571428571428" y="18.75" width="2.5" height="2.5"><!----></rect>
          <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!---->
          <rect fill="#fff" x="46.36904761904762" y="28.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.31304414835177696"
              repeatCount="indefinite"></animate>
          </rect>
          <rect fill="#fff" x="55.89285714285714" y="28.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.6228085071441938"
              repeatCount="indefinite"></animate>
          </rect><!----><!----><!----><!----><!---->
          <rect fill="#fff" x="113.03571428571428" y="28.75" width="2.5" height="2.5"><!----></rect><!----><!---->
          <rect fill="#fff" x="141.60714285714286" y="28.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.0850445251942615"
              repeatCount="indefinite"></animate>
          </rect>
          <rect fill="#fff" x="151.13095238095238" y="28.75" width="2.5" height="2.5"><!----></rect><!---->
          <rect fill="#fff" x="170.17857142857142" y="28.75" width="2.5" height="2.5"><!----></rect>
          <rect fill="#fff" x="179.70238095238096" y="28.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.1788393864110116"
              repeatCount="indefinite"></animate>
          </rect>
          <rect fill="#fff" x="189.22619047619048" y="28.75" width="2.5" height="2.5"><!----></rect><!---->
          <rect fill="#fff" x="17.797619047619047" y="38.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.10563593192407317"
              repeatCount="indefinite"></animate>
          </rect>
          <rect fill="#fff" x="27.32142857142857" y="38.75" width="2.5" height="2.5"><!----></rect><!----><!----><!---->
          <rect fill="#fff" x="65.41666666666667" y="38.75" width="2.5" height="2.5"><!----></rect><!---->
          <rect fill="#fff" x="84.46428571428571" y="38.75" width="2.5" height="2.5"><!----></rect>
          <rect fill="#fff" x="93.98809523809524" y="38.75" width="2.5" height="2.5"><!----></rect><!---->
          <rect fill="#fff" x="113.03571428571428" y="38.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.8443074027988429"
              repeatCount="indefinite"></animate>
          </rect><!----><!----><!----><!---->
          <rect fill="#fff" x="160.6547619047619" y="38.75" width="2.5" height="2.5">
            <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.2834211878525137"
              repeatCount="indefinite"></animate>
          </rect><!----><!----><!---->
          <rect fill="#fff" x="187.97619047619048" y="17.5" width="5" height="5">
            <animate attributeName="width" values="0;5" dur="2s" repeatCount="indefinite"></animate>
            <animate attributeName="height" values="0;5" dur="2s" repeatCount="indefinite"></animate>
            <animate attributeName="x" values="190.47619047619048;187.97619047619048" dur="2s" repeatCount="indefinite">
            </animate>
            <animate attributeName="y" values="20;17.5" dur="2s" repeatCount="indefinite"></animate>
          </rect>
          <rect fill="#fff" x="131.42857142857142" y="17.5" width="40" height="5">
            <animate attributeName="width" values="0;40;0" dur="2s" repeatCount="indefinite"></animate>
            <animate attributeName="x" values="171.42857142857142;131.42857142857142;171.42857142857142" dur="2s"
              repeatCount="indefinite"></animate>
          </rect>
        </svg>
      </div>
      <div class="title">
        <svg width="21px" height="20px"><polyline stroke-width="4" fill="transparent" points="10, 0 19, 10 10, 20" stroke="#1dc1f5"></polyline> <polyline stroke-width="2" fill="transparent" points="2, 0 11, 10 2, 20" stroke="#1dc1f5"></polyline></svg>
        <span style="margin-left: 15px;margin-right: 15px;background: linear-gradient(92deg, #0072ff, #00eaff 48.8525390625%, #01aaff);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">高性能并行化集成计算系统</span>
        <svg width="21px" height="20px"><polyline stroke-width="4" fill="transparent" points="11, 0 2, 10 11, 20" stroke="#1dc1f5"></polyline> <polyline stroke-width="2" fill="transparent" points="19, 0 10, 10 19, 20" stroke="#1dc1f5"></polyline></svg>
      </div>
      <div class="rectangle-right" >
        <span id="current-time"></span>
        <svg id="fullscreen-btn" style="margin-left: 10px;cursor: pointer;" class="fullscreen-btn" t="1749132943462" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5066" id="mx_n_1749132943463" width="20" height="20"><path d="M145.066667 85.333333h153.6c25.6 0 42.666667-17.066667 42.666666-42.666666S324.266667 0 298.666667 0H34.133333C25.6 0 17.066667 8.533333 8.533333 17.066667 0 25.6 0 34.133333 0 42.666667v256c0 25.6 17.066667 42.666667 42.666667 42.666666s42.666667-17.066667 42.666666-42.666666V145.066667l230.4 230.4c17.066667 17.066667 42.666667 17.066667 59.733334 0 17.066667-17.066667 17.066667-42.666667 0-59.733334L145.066667 85.333333z m170.666666 563.2L162.133333 802.133333l-76.8 76.8V725.333333C85.333333 699.733333 68.266667 682.666667 42.666667 682.666667s-42.666667 17.066667-42.666667 42.666666v256c0 25.6 17.066667 42.666667 42.666667 42.666667h256c25.6 0 42.666667-17.066667 42.666666-42.666667s-17.066667-42.666667-42.666666-42.666666H145.066667l76.8-76.8 153.6-153.6c17.066667-17.066667 17.066667-42.666667 0-59.733334-17.066667-17.066667-42.666667-17.066667-59.733334 0z m665.6 34.133334c-25.6 0-42.666667 17.066667-42.666666 42.666666v153.6l-76.8-76.8-153.6-153.6c-17.066667-17.066667-42.666667-17.066667-59.733334 0-17.066667 17.066667-17.066667 42.666667 0 59.733334l153.6 153.6 76.8 76.8H725.333333c-25.6 0-42.666667 17.066667-42.666666 42.666666s17.066667 42.666667 42.666666 42.666667h256c25.6 0 42.666667-17.066667 42.666667-42.666667v-256c0-25.6-17.066667-42.666667-42.666667-42.666666z m0-682.666667h-256c-25.6 0-42.666667 17.066667-42.666666 42.666667s17.066667 42.666667 42.666666 42.666666h153.6l-76.8 76.8-153.6 153.6c-17.066667 17.066667-17.066667 42.666667 0 59.733334 17.066667 17.066667 42.666667 17.066667 59.733334 0l153.6-153.6 76.8-76.8v153.6c0 25.6 17.066667 42.666667 42.666666 42.666666s42.666667-17.066667 42.666667-42.666666v-256c0-25.6-17.066667-42.666667-42.666667-42.666667z" fill="#ffffff" p-id="5067"></path></svg>
      </div>
    </div>
    <div class="content" style="padding: 20px;zoom: 2;">
      <div class="content-left" style="width: 50%;">
        <div class="content-title">
          <div class="icon"></div>
          <div class="content-title-text">累计统计</div>
        </div>
        <div style="display: flex; justify-content: space-between;gap: 10px;">
          <div class="module" >
            <div>
              <%= image_tag '/screen/1.svg' %>
            </div>
            <div>
              <div class="module-title">注册用户</div>
              <div style="display: flex;align-items: baseline;">
                <div class="module-number" id="stat-user">0</div>
                <div class="module-unit">人</div>
              </div>
              <div class="module-description">The total number of platform user</div>
            </div>
          </div>
          <div class="module">
            <%= image_tag '/screen/2.svg' %>
            <div>
              <div class="module-title">任务总计</div>
              <div style="display: flex;align-items: baseline;">
                <div class="module-number" id="stat-task">0</div>
                <div class="module-unit">次</div>
              </div>
              <div class="module-description">Total amount of script tasks</div>
            </div>
          </div>
          <div class="module">
            <div>
              <%= image_tag '/screen/3.svg' %>
            </div>
            <div>
              <div class="module-title">应用任务数</div>
              <div style="display: flex;align-items: baseline;">
                <div class="module-number" id="stat-app">0</div>
                <div class="module-unit">个</div>
              </div>
              <div class="module-description">The total amount of practical applications</div>
            </div>
          </div>
        </div>
        <div style="display: flex; justify-content: space-between;gap: 10px;margin-top: 10px;">
          <div class="module">
            <div>
              <%= image_tag '/screen/4.svg' %>
            </div>
            <div>
              <div class="module-title">CPU运行时长</div>
              <div style="display: flex;align-items: baseline;">
                <div class="module-number" id="stat-cpu">0</div>
                <div class="module-unit">核时</div>
              </div>
              <div class="module-description">The total duration of CPU operation</div>
            </div>
          </div>
          <div class="module">
            <div>
              <%= image_tag '/screen/5.svg' %>
            </div>
            <div>
              <div class="module-title">GPU运行时长</div>
              <div style="display: flex;align-items: baseline;">
                <div class="module-number" id="stat-gpu">0</div>
                <div class="module-unit">卡时</div>
              </div>
              <div class="module-description">The total duration of GPU operation</div>
            </div>
          </div>
          <div class="module">
            <div>
              <%= image_tag '/screen/6.svg' %>
            </div>
            <div>
              <div class="module-title">RAM运行时长</div>
              <div style="display: flex;align-items: baseline;">
                <div class="module-number" id="stat-ram">0</div>
                <div class="module-unit">GB时</div>
              </div>
              <div class="module-description">The total duration of RAM operation</div>
            </div>
          </div>
        </div>
        <div class="content-title" style="margin-top: 20px;">
          <div class="icon"></div>
          <div class="content-title-text">运行趋势</div>
        </div>
        <div id="run-trend" style="width: 100%;height: 280px;"></div>
        <div class="content-title">
          <div class="icon"></div>
          <div class="content-title-text">月度任务量</div>
        </div>
        <div id="employee-task" style="width: 100%;height: 280px;"></div>
      </div>
      <div class="content-right" style="width: 50%;">
        <div class="content-title" style="margin-bottom: 20px;">
          <div class="icon"></div>
          <div class="content-title-text">资源使用率</div>
        </div>
        <div class="content-right-title" style="display: flex;justify-content: space-between;gap: 10px;padding: 0 50px;">
          <div class="circle-area">
            <div>计算节点</div>
            <div id="chart1" style="width: 100px; height: 100px;"></div>
          </div>
          <div class="circle-area">
            <div>CPU</div>
            <div id="chart2" style="width: 100px; height: 100px;"></div>
          </div>
          <div class="circle-area">
            <div>GPU</div>
            <div id="chart3" style="width: 100px; height: 100px;"></div>
          </div>
          <div class="circle-area">
            <div>RAM</div>
            <div id="chart4" style="width: 100px; height: 100px;"></div>
          </div>
          <div class="circle-area">
            <div>Disk</div>
            <div id="chart5" style="width: 100px; height: 100px;"></div>
          </div>
        </div>
        <div class="content-title" style="margin-top: 40px;margin-bottom: 20px;">
          <div class="icon"></div>
          <div class="content-title-text">当前运行情况</div>
        </div>
        <div style="display: flex;justify-content: space-between;gap: 10px;color: #DAC844;">
          <div style="flex: 1;text-align: center;">
            <div style="font-size: 20px;font-weight: 500;">运行节点</div>
            <div class="node-box">
              <div id="node-running"></div>
            </div>
          </div>
          <div style="flex: 1;text-align: center;">
            <div style="font-size: 20px;font-weight: 500;">故障节点</div>
            <div class="node-box">
              <div id="node-error"></div>
            </div>
          </div>
          <div style="flex: 1;text-align: center;">
            <div style="font-size: 20px;font-weight: 500;">关闭节点</div>
            <div class="node-box">
              <div id="node-off"></div>
            </div>
          </div>
        </div>
        <div style="display: flex;justify-content: space-between;gap: 30px;margin-top: 30px;">
          <div style="display: flex;flex: 1;justify-content: space-between;flex-direction: column;color: #DAC844;font-size: 20px;font-weight: 500;">
            <div style="flex: 1;display: flex;justify-content: space-between;">
              <div>正在运行任务</div>
              <div id="task-running"></div>
            </div>
            <div
              style="width: 100%;height: 5px;display: flex;align-items: center;margin-top: 10px;margin-bottom: 10px;">
              <svg width="100%" height="5px">
                <rect x="0" y="2.5" width="200" height="1" fill="#3faacb">
                  <animate attributeName="width" from="1200" to="1200" dur="6s" calcMode="spline" keyTimes="0;1"
                    keySplines=".42,0,.58,1" repeatCount="indefinite"></animate>
                </rect>
                <rect x="0" y="2.5" width="1" height="1" fill="#fff">
                  <animate attributeName="x" from="0" to="1200" dur="5s" calcMode="spline" keyTimes="0;1"
                    keySplines="0.42,0,0.58,1" repeatCount="indefinite"></animate>
                </rect>
              </svg>
            </div>

            <div style="flex: 1;display: flex;justify-content: space-between;">
              <div>队列中任务</div>
              <div id="task-queue"></div>
            </div>
          </div>
          <div style="display: flex;flex: 1;justify-content: space-between;flex-direction: column;color: #618745;font-size: 20px;font-weight: 500;">
            <div style="flex: 1;display: flex;justify-content: space-between;">
              <div>正在运行实例</div>
              <div id="instance-running"></div>
            </div>
            <div
              style="width: 100%;height: 5px;display: flex;align-items: center;margin-top: 10px;margin-bottom: 10px;">
              <svg width="100%" height="5px">
                <rect x="0" y="2.5" width="200" height="1" fill="#3faacb">
                  <animate attributeName="width" from="1200" to="1200" dur="6s" calcMode="spline" keyTimes="0;1"
                    keySplines=".42,0,.58,1" repeatCount="indefinite"></animate>
                </rect>
                <rect x="0" y="2.5" width="1" height="1" fill="#fff">
                  <animate attributeName="x" from="0" to="1200" dur="5s" calcMode="spline" keyTimes="0;1"
                    keySplines="0.42,0,0.58,1" repeatCount="indefinite"></animate>
                </rect>
              </svg>
            </div>

            <div style="flex: 1;display: flex;justify-content: space-between;">
              <div>等待分配实例</div>
              <div id="instance-wait"></div>
            </div>
          </div>
        </div>
        <div style="display: flex;justify-content: space-between;gap: 5px;margin-top: 30px;color: #98D8FC;font-size: 20px;font-weight: 400;">
          <div style="background-color: rgba(30, 44, 71, 1);border: 1px solid rgba(50, 68, 100, 1);flex: 1;display: flex;height: 80px;justify-content: center;align-items: center;gap: 20px;">
            <div>平均计算时长</div>
            <div>
              <div class="progress-container">
                <div class="progress-bar" style="width: 50%"></div>
              </div>
            </div>
            <div>
              <span id="avg-calc" style="font-family: 'DINAlternate-Bold', 'DIN Alternate Bold', 'DIN Alternate', sans-serif;font-weight: 700;font-size: 30px;"></span>小时
            </div>
          </div>
          <div style="background-color: rgba(30, 44, 71, 1);border: 1px solid rgba(50, 68, 100, 1);flex: 1;display: flex;height: 80px;justify-content: center;align-items: center;gap: 20px;">
            <div>平均排队时间</div>
            <div>
              <div class="progress-container">
                <div class="progress-bar"></div>
              </div>
            </div>
            <div>
              <span id="avg-queue" style="font-family: 'DINAlternate-Bold', 'DIN Alternate Bold', 'DIN Alternate', sans-serif;font-weight: 700;font-size: 30px;"></span>小时
            </div>
          </div>
        </div>
        <div class="content-title" style="margin-top: 20px;">
          <div class="icon"></div>
          <div class="content-title-text">用户任务提交排行</div>
          <span style="font-family: 'Phosphate-Solid', 'Phosphate Solid', 'Phosphate Inline', 'Phosphate', sans-serif;font-weight:400;color:rgba(255, 255, 255, 0.4980392156862745);font-size: 18px;margin-left: 10px;">Top20</span>
        </div>
        <div style="display: flex;justify-content: space-between;gap: 30px;background-color: rgba(30, 44, 71, 1);">
          <div id="resources1" style="width: 50%;height: 340px;"></div>
          <div id="resources2" style="width: 50%;height: 340px;"></div>
        </div>
      </div>
    </div>
  </div>
  <%= javascript_include_tag 'echarts' %>
  <script>
    // =======================
    // 数据封装
    // =======================
    let dashboardData = {
      stats: {
        user: 20,
        task: 273,
        app: 74,
        cpu: 543,
        gpu: 87,
        ram: 1069
      },
      runTrend: {
        x: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        series: [
          [620, 932, 901, 1034, 1290, 1330, 1320, 1000, 1200, 1300, 1400, 1500],
          [500, 250, 600, 1200, 750, 800, 850, 50, 50, 50, 50, 50]
        ]
      },
      employeeTask: {
        x: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        series: [
          {
            name: 'Baidu',
            data: [620, 732, 701, 734, 1090, 1130, 1120],
            color: '#5d617e'
          },
          {
            name: 'Google',
            data: [120, 132, 101, 134, 290, 230, 220],
            color: '#f3673b'
          }
        ]
      },
      resourceUsage: [
        { id: 'chart1', value: 85, label: '计算节点' },
        { id: 'chart2', value: 65, label: 'CPU' },
        { id: 'chart3', value: 50, label: 'GPU' },
        { id: 'chart4', value: 75, label: 'RAM' },
        { id: 'chart5', value: 90, label: 'Disk' }
      ],
      nodeStatus: {
        running: [2, 4],
        error: [9],
        off: [3, 6]
      },
      taskStatus: {
        running: 12,
        queue: 27
      },
      instanceStatus: {
        running: 6,
        wait: 3
      },
      avg: {
        calc: 3.8,
        queue: 48
      },
      resources1: {
        y: ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'World'],
        data: [19325, 23438, 31000, 121594, 134141, 681807]
      },
      resources2: {
        y: ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'World'],
        data: [19325, 23438, 31000, 121594, 134141, 681807]
      }
    };

    // =======================
    // 渲染函数
    // =======================
    function renderStats(data) {
      document.getElementById('stat-user').textContent = data.stats.user;
      document.getElementById('stat-task').textContent = data.stats.task;
      document.getElementById('stat-app').textContent = data.stats.app;
      document.getElementById('stat-cpu').textContent = data.stats.cpu;
      document.getElementById('stat-gpu').textContent = data.stats.gpu;
      document.getElementById('stat-ram').textContent = data.stats.ram;
    }

    //function renderNodeStatus(data) {
    //  document.getElementById('node-running').innerHTML = data.nodeStatus.running.map(n => `<span class="node-number">${n}</span>`).join('');
    //  document.getElementById('node-error').innerHTML = data.nodeStatus.error.map(n => `<span class="node-number">${n}</span>`).join('');
      //document.getElementById('node-off').innerHTML = data.nodeStatus.off.map(n => `<span class="node-number">${n}</span>`).join('');
    //}
    function renderNodeStatus(data) {
      // 获取当前显示的数字
      const getCurrentNumbers = (elementId) => {
        const element = document.getElementById(elementId);
        return Array.from(element.getElementsByClassName('node-number'))
          .map(span => parseInt(span.textContent));
      };

      // 数字滚动动画
      const animateNumber = (element, start, end, duration = 1000) => {
        const startTime = performance.now();
        const updateNumber = (currentTime) => {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          const current = Math.round(start + (end - start) * progress);
          element.textContent = current;

          if (progress < 1) {
            requestAnimationFrame(updateNumber);
          }
        };
        requestAnimationFrame(updateNumber);
      };

      // 更新节点状态
      const updateNodeStatus = (elementId, newNumbers) => {
        const element = document.getElementById(elementId);
        const currentNumbers = getCurrentNumbers(elementId);

        // 确保数组长度一致
        while (currentNumbers.length < newNumbers.length) {
          currentNumbers.push(0);
        }

        // 创建新的数字元素
        const newElements = newNumbers.map((newNum, index) => {
          const span = document.createElement('span');
          span.className = 'node-number';
          if (index < currentNumbers.length) {
            animateNumber(span, currentNumbers[index], newNum);
          } else {
            span.textContent = newNum;
          }
          return span;
        });

        element.innerHTML = '';
        newElements.forEach(span => element.appendChild(span));
      };

      // 更新各个状态
      updateNodeStatus('node-running', data.nodeStatus.running);
      updateNodeStatus('node-error', data.nodeStatus.error);
      updateNodeStatus('node-off', data.nodeStatus.off);
    }

    function renderTaskInstanceStatus(data) {
      document.getElementById('task-running').textContent = data.taskStatus.running;
      document.getElementById('task-queue').textContent = data.taskStatus.queue;
      document.getElementById('instance-running').textContent = data.instanceStatus.running;
      document.getElementById('instance-wait').textContent = data.instanceStatus.wait;
    }

    function renderAvg(data) {
      document.getElementById('avg-calc').textContent = data.avg.calc;
      document.getElementById('avg-queue').textContent = data.avg.queue;
    }

    // 运行趋势图
    let runTrendChart = null;
    function renderRunTrend(data) {
      if (!runTrendChart) {
        runTrendChart = echarts.init(document.getElementById('run-trend'), 'dark');
      }
      const option = {
        backgroundColor: '#071031',
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.runTrend.x
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'CPU',
            data: data.runTrend.series[0],
            type: 'line',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 136, 212, 0.5)' },
                { offset: 1, color: 'rgba(0, 136, 212, 0)' }
              ])
            },
            symbolSize: 8,
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            }
          },
          {
            name: 'GPU',
            data: data.runTrend.series[1],
            type: 'line',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 212, 57, 0.5)' },
                { offset: 1, color: 'rgba(0, 212, 57, 0)' }
              ])
            },
            symbolSize: 8,
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            }
          }
        ]
      };
      runTrendChart.setOption(option, true);
    }

    // 员工任务量
    let employeeTaskChart = null;
    function renderEmployeeTask(data) {
      if (!employeeTaskChart) {
        employeeTaskChart = echarts.init(document.getElementById('employee-task'), 'dark');
      }
      const option = {
        backgroundColor: '#071031',
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: data.employeeTask.x
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: data.employeeTask.series.map(s => ({
          name: s.name,
          type: 'bar',
          stack: 'Search Engine',
          emphasis: { focus: 'series' },
          label: {
            show: s.name === 'Google',
            position: 'top',
            color: '#fff'
          },
          data: s.data,
          itemStyle: { color: s.color }
        }))
      };
      employeeTaskChart.setOption(option, true);
    }

    // 资源使用率
    let resourceUsageCharts = {};
    function renderResourceUsage(data) {
      const chartConfig = {
        radius: ['70%', '90%'],
        startAngle: 90,
        hoverAnimation: false,
        fontSize: 16,
        fontWeight: 700,
        colors: {
          active: '#00BCD4',
          inactive: '#1D2A45'
        }
      };
      data.resourceUsage.forEach(item => {
        if (!resourceUsageCharts[item.id]) {
          resourceUsageCharts[item.id] = echarts.init(document.getElementById(item.id));
        }
        const option = {
          series: [{
            type: 'pie',
            radius: chartConfig.radius,
            startAngle: chartConfig.startAngle,
            hoverAnimation: chartConfig.hoverAnimation,
            label: {
              show: true,
              position: 'center',
              formatter: `${item.value}%`,
              fontSize: chartConfig.fontSize,
              fontWeight: chartConfig.fontWeight,
              color: '#6B747F'
            },
            data: [
              {
                value: item.value,
                name: '进度',
                itemStyle: { color: chartConfig.colors.active }
              },
              {
                value: 100 - item.value,
                name: '剩余',
                itemStyle: { color: chartConfig.colors.inactive }
              }
            ]
          }]
        };
        resourceUsageCharts[item.id].setOption(option, true);
      });
    }

    // 资源Top20
    let resources1Chart = null;
    let resources2Chart = null;
    function renderResources(data) {
      if (!resources1Chart) {
        resources1Chart = echarts.init(document.getElementById('resources1'), 'dark');
      }
      if (!resources2Chart) {
        resources2Chart = echarts.init(document.getElementById('resources2'), 'dark');
      }
      const option1 = {
        backgroundColor: 'rgba(30, 44, 71, 1)',
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: data.resources1.y
        },
        series: [
          {
            name: '提交任务数',
            type: 'bar',
            data: data.resources1.data
          }
        ]
      };
      const option2 = {
        backgroundColor: 'rgba(30, 44, 71, 1)',
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '0%',
          right: '10%',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: data.resources2.y
        },
        series: [
          {
            name: '提交任务数',
            type: 'bar',
            data: data.resources2.data
          }
        ]
      };
      resources1Chart.setOption(option1, true);
      resources2Chart.setOption(option2, true);
    }

    // 总渲染
    function renderAll(data) {
      renderStats(data);
      renderNodeStatus(data);
      renderTaskInstanceStatus(data);
      renderAvg(data);
      renderRunTrend(data);
      renderEmployeeTask(data);
      renderResourceUsage(data);
      renderResources(data);
    }

    // =======================
    // 模拟ajax拉取数据
    // =======================
    // function fetchDashboardData() {
    //   // 这里可以用fetch/ajax请求后端接口，当前用setTimeout模拟
    //   return new Promise(resolve => {
    //     setTimeout(() => {
    //       // 可以在这里模拟数据变化
    //       // 例如：dashboardData.stats.user += Math.floor(Math.random()*2);
    //       resolve(JSON.parse(JSON.stringify(dashboardData)));
    //     }, 300);
    //   });
    // }

    function fetchDashboardData() {
      // 使用AJAX请求后端接口
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', '/pun/sys/dashboard/get_screen_data', true); // 请根据实际接口地址修改
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            if (xhr.status === 200) {
              try {
                const data = JSON.parse(xhr.responseText);
                resolve(data);
              } catch (e) {
                reject(e);
              }
            } else {
              reject(new Error('请求失败，状态码: ' + xhr.status));
            }
          }
        };
        xhr.send();
      });
    }

    // =======================
    // 定时刷新
    // =======================
    async function refreshDashboard() {
      console.log('refreshDashboard');
      const data = await fetchDashboardData();
      renderAll(data);
    }

    // 首次渲染
    refreshDashboard();
    // 每30秒刷新一次
    setInterval(refreshDashboard, 30000);

    // =======================
    // 其它功能
    // =======================
    document.getElementById('fullscreen-btn').addEventListener('click', function() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    });

    function updateTime() {
      const now = new Date();
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const weekday = weekdays[now.getDay()];
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      document.getElementById('current-time').textContent =
        `${year}年${month}月${day}日  ${weekday}  ${hours}:${minutes}:${seconds}`;
    }
    updateTime();
    setInterval(updateTime, 1000);

    // 适应窗口大小
    window.addEventListener('resize', function() {
      runTrendChart && runTrendChart.resize();
      employeeTaskChart && employeeTaskChart.resize();
      Object.values(resourceUsageCharts).forEach(chart => chart && chart.resize());
      resources1Chart && resources1Chart.resize();
      resources2Chart && resources2Chart.resize();
    });
  </script>
</body>
</html>
