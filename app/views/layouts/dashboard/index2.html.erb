<!-- <%= render partial: 'shared/welcome' %>

<%- dashboard_layout.fetch(:rows, []).each do |row| -%>
<div class="row">
  <%- row.fetch(:columns, []).each do |col| -%>
  <div class='<%= "col-md-#{col[:width]}" %>'> <%# FIXME: what if width is not specified!? %>
    <%- Array(col.fetch(:widgets, [])).each do |widget| -%>
      <%= widget.to_s %>
      <%= render_widget(widget.to_s) %>
    <%- end -%>
  </div>
  <%- end -%>
</div>
<%- end -%> -->
<!DOCTYPE html>
<html>

<head>
  <title><%= content_for?(:title) ? yield(:title) : "超算应用平台|华信鼎成" %></title>
  <%= favicon_link_tag 'favicon.ico', href: OodAppkit.public.url.join('favicon.ico'), skip_pipeline: true %>

  <!-- Webpacker -->
  <%#= javascript_pack_tag 'application' %>
  <%#= stylesheet_pack_tag 'application', media: 'all' %>

  <!-- (Legacy) Sprockets -->
  <%#= stylesheet_link_tag 'application', media: 'all' %>
  <%#= javascript_include_tag 'application' %>
  <%#= javascript_include_tag 'turbolinks' if Configuration.turbolinks_enabled? %>

  <%= csrf_meta_tags %>

  <%= yield :head %>

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <% if Configuration.turbolinks_enabled? %>
    <meta name="turbolinks-root" content="<%= ENV['RAILS_RELATIVE_URL_ROOT'] || "/" %>">
  <% end %>
  <%= render partial: '/layouts/nav/styles', locals: { bg_color: Configuration.brand_bg_color, link_active_color: Configuration.brand_link_active_bg_color } %>
  <!-- <script src="https://cdn.staticfile.org/jquery/1.10.2/jquery.min.js"></script> -->
  <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->


  <%= javascript_pack_tag 'application' %>
  <%= stylesheet_pack_tag 'application', media: 'all' %>

  <!-- (Legacy) Sprockets -->
  <%= stylesheet_link_tag 'application', media: 'all' %>
  <%= javascript_include_tag 'application' %>
  <%= javascript_include_tag 'turbolinks' if Configuration.turbolinks_enabled? %>
  <%#= stylesheet_link_tag 'font-awesome', media: 'all' %>
  <!-- <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous"> -->
  <!-- <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"> -->
  <!-- <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css" integrity="sha384-50oBUHEmvpQ+1lW4y57PTFmhCaXp0ML5d60M1M7uH2+nqUivzIebhndOJK28anvf" crossorigin="anonymous"> -->

  <!-- 可选的 Bootstrap 主题文件（一般不用引入） -->
  <!-- <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap-theme.min.css" integrity="sha384-6pzBo3FDv/PJ8r2KRkGHifhEocL+1X2rVCTTkUfGk7/0pbek5mMa1upzvWbrUbOZ" crossorigin="anonymous"> -->
  <%#= stylesheet_link_tag 'application', media: 'all' %>
  <!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
  <!-- <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script> -->
  <%#= stylesheet_pack_tag 'application', media: 'all' %>

  <!-- (Legacy) Sprockets -->
  <%#= stylesheet_link_tag 'application', media: 'all' %>
  <style>
    .header {
      background-image: linear-gradient(145deg, #031c37, #07487a);
      color: white;
      height: 80px;
      font-size: 18px;
    }

    .left {
      width: 417px;
      float: left;
      color: #829ebc;
      background-color: #031c37;
      background: #031c37 url('<%= image_path "bg.png" %>') no-repeat right bottom;
      height: calc(100vh - 80px);
      padding: 20px;
      overflow: scroll;
    }

    .left .title {
      cursor: pointer;
      margin-top: 20px;
      user-select: none;
      font-size: 20px;
    }

    .left .title img {
      margin-top: -2px;
      margin-left: 2px;
    }

    .body {
      width: 1000px;
      float: left;
      background-color: white;
      height: calc(100vh - 80px);
      width: calc(100vw - 550px);
    }

    .con {
      display: flex;
      flex-flow: row wrap;
      justify-content: left;
      align-items: center;
    }

    .apps {
      margin-top: 50px;
    }

    .apps-icon {
      margin-top: 20px;
      margin-right: 6px;
      margin-left: 6px;
    }

    .apps-icon img {
      width: 48px;
      height: 50px;
      margin-right: 12px;
    }

    .shousuo, .zhankai {
      width: 31px;
      height: 26px;
      float: right;
      cursor: pointer;
    }

    .logo {
      height: 50px;
      margin: 18px 0px 0px 7px;
    }

    .header-right {
      float: right;
      display: flex;
    }

    .header-right .username {
      margin-top: 28px;
      margin-right: 50px;
    }

    .help {
      display: flex;
    }

    .help img {
      width: 32px;
      height: 30px;
      margin: 26px 10px 0px 0px;
      cursor: pointer;
    }

    .help .dropdown {
      color: white;
      cursor: pointer;
      margin-top: 28px;
    }

    .help a {
      cursor: pointer;
      padding-left: 15px;
    }

    .logout {
      display: flex;
    }

    .logout img {
      width: 32px;
      height: 30px;
      margin: 26px 10px 0px 0px;
      cursor: pointer;
    }

    .logout .logout-text {
      margin: 28px 40px 0px 0px;
      cursor: pointer;
    }

    .split-line {
      margin: 20px 20px 0px 20px;
      border-left: 1px solid #1c5885;
      height: 40px;
    }

    .right {
      width: 100px;
      float: right;
      height: calc(100vh - 80px);
      display: flex;
    }

    .sub-right {
      align-self: center;
      font-size: 18px;
      color: #036bbb;
    }

    .sub-right .module {
      margin-bottom: 40px;
    }

    .sub-right .module1 {
      margin-bottom: 40px;
    }

    .sub-right img {
      margin-left: 12px;
      margin-bottom: 5px;
    }

    .home-footer {
      background-color: #d3d3d3;
      height: 56px;
      color: white;
      font-size: 18px;
      text-align: left;
    }

    .app-text {
      margin-left: -10px;
      color: #829ebc;
    }

    .app-card {
      background: transparent;
    }

  </style>
</head>

<body>
  <% own_valid_license = Cipher.get_license_list&.pluck(:status)&.include?("激活") %>
  <% if @user.name != 'admin' && !own_valid_license %>
    <div style="width:100%; position:relative;z-index:1;margin:0 auto; background: #000;">
      <div style="margin-top: 80px;width:100%;position:absolute;z-index:2;text-align:center;background:#0000005e;height: calc(100vh - 80px);">
        <h2 style="color:#000000;padding-top: 200px;">请导入 License 许可！</h2>
      </div>
    </div>
  <% end %>
  <% terminal_link = '' %>
  <div class="header">
    <%= image_tag 'logo.png', class: 'logo' %>
    <!-- <img src="logo.png" class="logo"> -->
    <div class="header-right">
      <div class="username"><%= @user.name %></div>
      <div class="help">
        <!-- <img src="help.png"> -->
        <%= image_tag 'help.png' %>
        <div class="dropdown">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" style="color: white;">帮助 <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="javascript:;" class="change_pwd">修改用户密码</a></li>
            <li><a href="http://*************:3000/?orgId=1&kiosk&refresh=15s" target="_blank">集群监控</a></li>
            <li><a href="/nginx/stop?redir=/pun/sys/dashboard/">重启网页服务器</a></li>
          </ul>
        </div>
      </div>
      <div class="split-line"></div>
      <div class="logout">
        <!-- <img src="logout.png"> -->
        <%= image_tag 'logout.png' %>
        <a class="logout-text" href="/logout" style="color: white;">退出</a>
        <!-- <div class="logout-text">退出</div> -->
      </div>
    </div>
  </div>

  <div class="container-fiuld">
    <div class="left">
      <div>
        <%= image_tag 'shousuo.png', class: 'shousuo' %>
        <%= image_tag 'zhankai.png', class: 'zhankai', style: 'display: none;' %>
        <!-- <img src="shousuo.png" class="shousuo">
        <img src="zhankai.png" class="zhankai" style="display: none;"> -->
      </div>
      <div class="apps">
        <div class="title">
          桌面应用
          <%= image_tag 'xia.png', class: 'xia' %>
          <%= image_tag 'you.png', class: 'you', style: 'display: none;' %>
          <!-- <img src="xia.png" class="xia">
          <img src="you.png" class="you" style="display: none;"> -->
        </div>
        <div class="con">
          <!-- <%- dashboard_layout.fetch(:rows, []).each do |row| -%>
            <div class="row">
              <%- row.fetch(:columns, []).each do |col| -%>
              <div class='<%= "col-md-#{col[:width]}" %>'> <%# FIXME: what if width is not specified!? %>
                <%- Array(col.fetch(:widgets, [])).each do |widget| -%>
                  <%= widget.to_s %>
                  <%= render_widget(widget.to_s) %>
                <%- end -%>
              </div>
              <%- end -%>
            </div>
          <%- end -%> -->
          <% @pinned_apps.each do |app| %>
            <%- link = app.links.first -%>
            <% terminal_link = link.url.to_s if link.icon_uri.to_s == 'fas://terminal' %>
            <% next if link.icon_uri.to_s.match?(/:\/\//) %>
            <div class="apps-icon">
              <%=
                link_to(
                  link.url.to_s,
                  class: "app-card",
                  target: "_blank"
                ) do
              %>
                <div class="center-block text-center">
                  <%#= link.icon_uri %>
                  <%= icon_tag(link.icon_uri) %>
                  <div class="app-text"><%= content_tag(:p, link.title) %></div>
                </div>
              <% end %>
              <%#= image_tag '1664702066912.jpg' %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    <div class="body">
      <iframe src="" frameborder="0" style="width: 100%;height: calc(100vh - 85px);"></iframe>
      <!-- <div class="home-footer">
        <img src="./origin-logo.png" alt="" style="height: 55px;">
        <div style="float: right;margin-top:15px;margin-right: 20px;">2022.10.3</div>
      </div> -->
    </div>
    <div class="right">
      <div class="sub-right">
        <div class="module nav-tree">
          <div>
            <%= image_tag 'right/用户目录.png' %>
            <!-- <img src="right/用户目录.png"> -->
          </div>
          <div>
            用户目录
          </div>
        </div>
        <div class="module nav-desktop">
          <div>
            <%= image_tag 'right/云台.png' %>
            <!-- <img src="right/云台.png"> -->
          </div>
          <div>
            云台桌面
          </div>
        </div>
        <div class="module nav-task">
          <div>
            <%= image_tag 'right/任务.png' %>
            <!-- <img src="right/任务.png"> -->
          </div>
          <div>
            任务状态
          </div>
        </div>
        <div class="module1 nav-submit">
          <div>
            <%= image_tag 'right/业务.png' %>
            <!-- <img src="right/业务.png"> -->
          </div>
          <div>
            提交业务
          </div>
        </div>
        <div class="module1 nav-shell">
          <div>
            <%= image_tag 'right/终端.png' %>
            <!-- <img src="right/终端.png"> -->
          </div>
          <div>
            Shell终端
          </div>
        </div>
        <% if @user.name == 'admin' %>
          <div class="module1 nav-system">
            <div>
              <%= image_tag 'right/system.png' %>
              <!-- <img src="right/system.png"> -->
            </div>
            <div>
              系统管理
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <script>
    $(".title").click(function(){
      $(this).next().toggle();
      $(this).find('.xia').toggle();
      $(this).find('.you').toggle();

      // 触发展开事件
      $(".zhankai").hide();
      $(".shousuo").show();
      $(".left").css("width", "417px")
      $(".body").css("width", "calc(100vw - 550px)")
    });

    $(".shousuo").click(function(){
      $(".con").hide();
      $(this).hide();
      $(".zhankai").show();
      $(".left").css("width", "160px")
      $(".body").css("width", "calc(100vw - 294px)")
    });

    $(".zhankai").click(function(){
      $(this).hide();
      $(".shousuo").show();
      $(".con").show();
      $(".left").css("width", "417px")
      $(".body").css("width", "calc(100vw - 550px)")
    });

    $(".nav-desktop").click(function(){
      $("iframe").attr('src', "/pun/sys/dashboard/batch_connect/sys/bc_desktop/session_contexts/new");
    });

    $(".nav-tree").click(function(){
      $("iframe").attr('src', "<%= files_path(Dir.home) %>");
    });

    $(".nav-task").click(function(){
      $("iframe").attr('src', "/pun/sys/dashboard/activejobs");
    });

    $(".nav-submit").click(function(){
      window.open("/pun/sys/dashboard/apps/show/myjobs");
    });

    $(".nav-shell").click(function(){
      window.open("<%= terminal_link %>");
    });

    $(".module").click(function(){
      $(".shousuo").click();
    });

    $(".nav-system").click(function(){
      window.open("/pun/sys/dashboard/admin_dashboard");
    });

    $(".logo").click(function(){
      window.location.href = "/pun/sys/dashboard";
    })

    $(".change_pwd").click(function(){
      $("iframe").attr('src', "/pun/sys/dashboard/user/change_password");
    });
  </script>
</body>

</html>
