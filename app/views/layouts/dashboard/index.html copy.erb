<!DOCTYPE html>
<html>

<head>
  <title><%= content_for?(:title) ? yield(:title) : "超算应用平台|华信鼎成" %></title>
  <%= favicon_link_tag 'favicon.ico', href: OodAppkit.public.url.join('favicon.ico'), skip_pipeline: true %>

  <%= csrf_meta_tags %>

  <%= yield :head %>

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <% if Configuration.turbolinks_enabled? %>
    <meta name="turbolinks-root" content="<%= ENV['RAILS_RELATIVE_URL_ROOT'] || "/" %>">
  <% end %>
  <%= render partial: '/layouts/nav/styles', locals: { bg_color: Configuration.brand_bg_color, link_active_color: Configuration.brand_link_active_bg_color } %>
  <%= javascript_pack_tag 'application' %>
  <%= stylesheet_pack_tag 'application', media: 'all' %>

  <!-- (Legacy) Sprockets -->
  <%= stylesheet_link_tag 'application', media: 'all' %>
  <%= javascript_include_tag 'application' %>
  <%= javascript_include_tag 'turbolinks' if Configuration.turbolinks_enabled? %>
  <style>
    body {
      background-image: url('<%= image_path "/new/home/<USER>" %>');
      background-repeat: no-repeat;
      background-size: cover;      /* 让背景图铺满整个页面 */
      background-position: center; /* 居中显示 */
    }
  </style>
  <style>
    .dock {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(255, 255, 255, 0);
      padding: 10px 20px;
      border-radius: 16px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.2);
      display: flex;
      gap: 24px;
      z-index: 999;
      backdrop-filter: blur(20px);
    }

    .dock-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      color: #333;
    }

    .dock-item img {
      width: 48px;
      height: 48px;
      transition: transform 0.3s ease;
    }

    .dock-item span {
      margin-top: 6px;
    }

    /* 分割线样式，仅用于第一个图标后 */
    .with-divider {
      position: relative;
      padding-right: 24px;
      margin-right: 24px;
    }

    .with-divider::after {
      content: "";
      position: absolute;
      top: 10%;
      right: 0;
      width: 1px;
      height: 80%;
      background-color: #ccc;
    }
    .left1 {
      width: 300px;
      height: 380px;
      border-radius: 16px;
      background: white;
      display: flex;
      justify-content: center;
    }
    .left2 {
      width: 300px;
      height: 344px;
      border-radius: 16px;
      background: white;
      margin-top: 20px;
      padding-left: 26px;
      padding-right: 26px;
      padding-top: 1px;
    }

    .right1 {
      width: 960px;height: 208px;border-radius: 16px;background: white;
    }

    .right2 {
      display: flex;gap: 12px;margin-top: 20px;
    }

    .right3 {
      width: 960px;height: 596px;border-radius: 16px;background: white;margin-top: 20px;
    }

    .avatar {
      height: 64px;
      width: 64px;
      border-radius: 50%;
    }
  </style>
</head>

<body>
  <% own_valid_license = Cipher.get_license_list&.pluck(:status)&.include?("激活") %>
  <% if @user.name != 'admin' && !own_valid_license %>
    <div style="width:100%; position:relative;z-index:1;margin:0 auto; background: #000;">
      <div style="margin-top: 80px;width:100%;position:absolute;z-index:2;text-align:center;background:#0000005e;height: calc(100vh - 80px);">
        <h2 style="color:#000000;padding-top: 200px;">请导入 License 许可！</h2>
      </div>
    </div>
  <% end %>

  <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
    <div style="max-width: 1280px;">
      <div style="display: flex;gap: 20px;">
        <div>
          <div style="height: 60px;margin: 16px 0;"></div>
          <div class="left1">
            <div style="text-align:center;margin-top: -20px;">
              <div>
                <%= image_tag "/new/home/<USER>", class: "avatar" %>
              </div>
              <div>
                <%= @user.name %>
              </div>
              <div style="color: #007AFF">
                账号设置
              </div>
              <div style="margin-top: 16px;width: 248px;text-align: left;">
                剩余机时
              </div>
              <div style="display: flex;gap: 32px;justify-content: space-around;margin-top:8px;">
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div>3.4小时</div>
                </div>
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div>2.4小时</div>
                </div>
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div>1.8小时</div>
                </div>
              </div>
              <div style="display: flex;justify-content: left;flex-direction: column;">
                <div style="margin-top: 24px;width: 248px;text-align: left;">
                  账户余额
                </div>
                <div style="display: flex;align-items: baseline;">
                  <div>¥</div>
                  <div style="font-size: 20px;font-weight: 600;">152.6</div>
                </div>
              </div>


              <div style="display: flex;margin: 4px 0px">
                <div style="display: flex;align-items: center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 20px;" %></div>
                  <div style="color: #007AFF">账户记录</div>
                </div>
                <div style="display: flex;margin-left: 16px;align-items: center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 20px;" %></div>
                  <div style="color: #007AFF">扫码充值</div>
                </div>
              </div>
              <div style="width: 248px;text-align:start;">
                优先扣除剩余机时，任有一项硬配时长不足将按计费标准扣除余额。
              </div>
            </div>
          </div>
          <div class="left2">
            <div>
              <div style="margin-top: 24px;width: 248px;text-align: left;">
                累计耗时
              </div>
              <div style="margin-top: 4px;width: 248px;text-align: left;">
                12时05分
              </div>
              <div style="margin-top: 4px;width: 248px;text-align: left;">
                成功完成28次AI训练，机时平均消耗3 分16秒
              </div>
            </div>
            <div>
              <div style="margin-top: 24px;width: 248px;text-align: left;color:#666666;">
                我的模板
              </div>
              <div style="display: flex; flex-wrap: wrap; gap: 14px;margin-top: 8px;">
                <% items = [
                  { image: "/new/home/<USER>", label: "新建模板" },
                  { image: "/new/home/<USER>", label: "自制模板" },
                  { image: "/new/home/<USER>", label: "自制模板" },
                  { image: "/new/home/<USER>", label: "自制模板" },
                  { image: "/new/home/<USER>", label: "自制模板" }
                ] %>

                <% items.each do |item| %>
                  <div style="width: calc(33.333% - 10.33px); text-align: center;">
                    <div><%= image_tag item[:image], style: "width: 40px;" %></div>
                    <div><%= item[:label] %></div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="header"><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
          <div class="right1"></div>
          <div class="right2">
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
          </div>
          <div class="right3"></div>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="dock">
    <div class="dock-item with-divider">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>待提交</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>队列中 3</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>进行中 2</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>已完成 5</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>已终止 4</span>
    </div>
  </div> -->
  <%#= image_tag '/new/home_bg.png' %>
</body>

</html>
