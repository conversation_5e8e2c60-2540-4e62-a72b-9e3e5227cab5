<div class="right1">
  <div class="circle-row">
    <div class="circle-col">
      <div style="padding-left: 10px;">
        <span style="font-weight: 600;">节点</span> <span id="nodes-total">总数--</span>
      </div>
      <canvas id="circleCanvas0" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
    </div>
    <div class="circle-col">
      <div style="padding-left: 10px;">
        <span style="font-weight: 600;">CPU</span> <span id="cpu-total">总核--</span>
      </div>
      <canvas id="circleCanvas1" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
    </div>
    <div class="circle-col">
      <div style="padding-left: 10px;">
        <span style="font-weight: 600;">GPU</span> <span id="gpu-total">总卡数--</span>
      </div>
      <canvas id="circleCanvas2" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
    </div>
    <div class="circle-col">
      <div style="padding-left: 10px;">
        <span style="font-weight: 600;">RAM</span> <span id="ram-total">总容量--</span>
      </div>
      <canvas id="circleCanvas3" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
    </div>
    <div class="circle-col">
      <div style="padding-left: 10px;">
        <span style="font-weight: 600;">Disk</span> <span id="disk-total">总空间--</span>
      </div>
      <canvas id="circleCanvas4" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
    </div>
  </div>
</div>

<script>
  // 图表配置
  const displaySize = 132;
  const radius = 52;
  const lineWidth = 12;
  const dpr = window.devicePixelRatio || 1;

  // 默认圆环数据（加载时显示）
  let circleData = [
    { percent: 0, lines: ['--', ' 加载中'], color: '#8373eb' },
    { percent: 0, lines: ['--', ' 加载中'], color: '#34c759' },
    { percent: 0, lines: ['--', ' 加载中'], color: '#ff9500' },
    { percent: 0, lines: ['--', ' 加载中'], color: '#ff3b30' },
    { percent: 0, lines: ['--', ' 加载中'], color: '#1e90ff' }
  ];

  function drawCircle(canvas, percent, lines, color) {
    const ctx = canvas.getContext('2d');
    // 设置实际像素尺寸
    canvas.width = displaySize * dpr;
    canvas.height = displaySize * dpr;
    canvas.style.width = displaySize + 'px';
    canvas.style.height = displaySize + 'px';
    ctx.setTransform(1, 0, 0, 1, 0, 0); // 重置变换
    ctx.scale(dpr, dpr);

    const centerX = displaySize / 2;
    const centerY = displaySize / 2;

    ctx.clearRect(0, 0, displaySize, displaySize);

    // 背景圆
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#f2f6ff';
    ctx.lineWidth = lineWidth;
    ctx.lineCap = 'round';
    ctx.stroke();

    // 进度圆弧
    ctx.beginPath();
    ctx.arc(
      centerX,
      centerY,
      radius,
      -Math.PI / 2,
      -Math.PI / 2 + (2 * Math.PI * percent) / 100,
      false
    );
    ctx.strokeStyle = color || '#8373eb';
    ctx.lineWidth = lineWidth;
    ctx.lineCap = 'round';
    ctx.stroke();

    // 百分比文本（上移，数字20px，百分号14px，底对齐）
    const percentStr = percent.toString();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'alphabetic';
    // 计算总宽度
    ctx.font = 'bold 20px Arial, Helvetica, sans-serif';
    const percentNumWidth = ctx.measureText(percentStr).width;
    ctx.font = '14px Arial, Helvetica, sans-serif';
    const percentSignWidth = ctx.measureText('%').width;
    const totalWidth = percentNumWidth + percentSignWidth + 2; // 2px间距

    // 百分比数字
    // 上移 18px（原来是 centerY - 22 + 20），现在 centerY - 40 + 20
    const percentBaseY = centerY - 40 + 25; // 上移18px
    let percentNumX = centerX - totalWidth / 2 + percentNumWidth / 2;
    ctx.font = 'bold 20px Arial, Helvetica, sans-serif';
    ctx.fillStyle = '#1A1A1A';
    ctx.fillText(percentStr, percentNumX, percentBaseY);

    // 百分号
    let percentSignX = percentNumX + percentNumWidth / 2 + percentSignWidth / 2 + 2;
    ctx.font = '14px Arial, Helvetica, sans-serif';
    ctx.fillText('%', percentSignX, percentBaseY);

    // 分割线
    const lineY = centerY - 4;
    ctx.beginPath();
    ctx.moveTo(centerX - 32, lineY);
    ctx.lineTo(centerX + 32, lineY);
    ctx.strokeStyle = '#e5e5e5';
    ctx.lineWidth = 1;
    ctx.stroke();

    // 下方两行文字
    // 第一行 12px
    ctx.font = '12px Arial, Helvetica, sans-serif';
    ctx.fillStyle = '#666';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(lines[0], centerX, centerY + 12);
    // 第二行 10px
    ctx.font = '10px Arial, Helvetica, sans-serif';
    ctx.fillText(lines[1], centerX, centerY + 32 - 5);
  }

  // 更新图表数据
  function updateChartData(data) {
    // 更新标题显示
    document.getElementById('nodes-total').textContent = `总数${data.nodes.total}`;
    document.getElementById('cpu-total').textContent = `总核${data.cpu.total}`;
    document.getElementById('gpu-total').textContent = `总卡数${data.gpu.total}`;
    document.getElementById('ram-total').textContent = `总容量${data.ram.total}`;
    document.getElementById('disk-total').textContent = `总空间${data.disk.total}`;

    // 更新圆环数据
    circleData = [
      {
        percent: data.nodes.percent,
        lines: [data.nodes.free.toString(), ' 空闲'],
        color: '#8373eb'
      },
      {
        percent: data.cpu.percent,
        lines: [data.cpu.free.toString(), ' 空闲'],
        color: '#34c759'
      },
      {
        percent: data.gpu.percent,
        lines: [data.gpu.free.toString(), ' 空闲'],
        color: '#ff9500'
      },
      {
        percent: data.ram.percent,
        lines: [data.ram.free, ' 空闲'],
        color: '#ff3b30'
      },
      {
        percent: data.disk.percent,
        lines: [data.disk.free, ' 空闲'],
        color: '#1e90ff'
      }
    ];

    // 重新渲染所有圆环
    renderAllCharts();
  }

  // 渲染所有圆环
  function renderAllCharts() {
    for (let i = 0; i < 5; i++) {
      const canvas = document.getElementById('circleCanvas' + i);
      if (canvas) {
        drawCircle(canvas, circleData[i].percent, circleData[i].lines, circleData[i].color);
      }
    }
  }

  // 获取数据并更新图表
  function fetchAndUpdateData() {
    fetch('/pun/sys/dashboard/get_right1_data')
      .then(response => response.json())
      .then(data => {
        updateChartData(data);
      })
      .catch(error => {
        console.error('获取数据失败:', error);
        // 显示错误状态
        circleData = [
          { percent: 0, lines: ['--', ' 错误'], color: '#8373eb' },
          { percent: 0, lines: ['--', ' 错误'], color: '#34c759' },
          { percent: 0, lines: ['--', ' 错误'], color: '#ff9500' },
          { percent: 0, lines: ['--', ' 错误'], color: '#ff3b30' },
          { percent: 0, lines: ['--', ' 错误'], color: '#1e90ff' }
        ];
        renderAllCharts();
      });
  }

  // 页面加载完成后初始化
  document.addEventListener('DOMContentLoaded', function() {
    // 初始渲染
    renderAllCharts();
    // 获取数据并更新
    fetchAndUpdateData();
    // 设置定时更新（每30秒）
    setInterval(fetchAndUpdateData, 30000);
  });
</script>