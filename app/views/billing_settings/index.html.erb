<% content_for :title, "计费设置" %>

<div class="row">
  <div class="col-md-8">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-cog me-2"></i>费率配置
        </h5>
      </div>
      <div class="card-body">
        <%= form_with model: @machine_time_change, url: billing_settings_path, method: :patch, local: true, class: "row g-3" do |form| %>
          <div class="col-md-4">
            <%= form.label :cpu, "CPU费率 (元/核/时)", class: "form-label" %>
            <%= form.number_field :cpu, step: 0.01, min: 0, class: "form-control", placeholder: "10.00" %>
          </div>

          <div class="col-md-4">
            <%= form.label :gpu, "GPU费率 (元/卡/时)", class: "form-label" %>
            <%= form.number_field :gpu, step: 0.01, min: 0, class: "form-control", placeholder: "10.00" %>
          </div>

          <div class="col-md-4">
            <%= form.label :ram, "RAM费率 (元/GB/时)", class: "form-label" %>
            <%= form.number_field :ram, step: 0.01, min: 0, class: "form-control", placeholder: "10.00" %>
          </div>

          <div class="col-12">
            <%= form.submit "更新费率", class: "btn btn-primary me-2" %>
            <%#= link_to "手动执行扣费", run_billing_billing_settings_path, method: :post,
                        class: "btn btn-warning",
                        confirm: "确定要立即执行扣费任务吗？这将扫描昨天的所有用户任务并进行扣费。" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-info-circle me-2"></i>说明
        </h5>
      </div>
      <div class="card-body">
        <ul class="list-unstyled">
          <li><strong>CPU费率:</strong> 按核心数和使用时间计费</li>
          <li><strong>GPU费率:</strong> 按GPU卡数和使用时间计费</li>
          <li><strong>RAM费率:</strong> 按内存GB数和使用时间计费</li>
        </ul>

        <hr>

        <h6>自动扣费时间</h6>
        <p class="text-muted small">
          系统每天凌晨2点自动执行扣费任务，扫描前一天的所有用户任务使用情况并进行扣费。
        </p>

        <h6>扣费规则</h6>
        <p class="text-muted small">
          - 只对已完成、失败或取消的任务进行扣费<br>
          - 扣费记录会保存到账户日志中<br>
          - 用户余额会同步更新到用户信息表
        </p>
      </div>
    </div>
  </div>
</div>

<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-history me-2"></i>最近扣费记录
        </h5>
      </div>
      <div class="card-body">
        <% if @recent_billing_logs.any? %>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>用户名</th>
                  <th>扣费金额</th>
                  <th>余额</th>
                  <th>描述</th>
                  <th>时间</th>
                </tr>
              </thead>
              <tbody>
                <% @recent_billing_logs.each do |log| %>
                  <tr>
                    <td><%= log.username %></td>
                    <td class="text-danger">
                      <%= number_to_currency(log.changed_balance.abs, unit: "¥") %>
                    </td>
                    <td>
                      <span class="<%= log.after_balance < 0 ? 'text-danger' : 'text-success' %>">
                        <%= number_to_currency(log.after_balance, unit: "¥") %>
                      </span>
                    </td>
                    <td>
                      <small class="text-muted"><%= log.description %></small>
                    </td>
                    <td>
                      <small class="text-muted">
                        <%= log.created_at.strftime('%Y-%m-%d %H:%M') %>
                      </small>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <div class="text-center text-muted py-4">
            <i class="fas fa-inbox fa-3x mb-3"></i>
            <p>暂无扣费记录</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<%= render 'layouts/dashboard/base' %>
