<div class="container-fluid">
  <div class="row" style="margin-bottom: 10px;">
    <%= form_tag '/pun/sys/dashboard/admin_dashboard/user_bills', method: :get, class: 'form-inline' do |form| %>
      <div class='form-group'>
        <%= select_tag :label, options_for_select([ "用户账号", "备注" ], params[:label] || "用户账号"), class: 'custom-select', style: "margin-right: 5px;" %>
        <%= text_field_tag :username, params[:username] || nil, class: 'form-control', style: "margin-right: 5px;" %>
      </div>

      <div class='form-group' style="margin-right: 5px;">
        <!-- <label for="start_time">开始时间:</label> -->
        <%= datetime_field_tag :start_time, params[:start_time] || nil, class: 'form-control', style: "margin-right: 5px;", placeholder: 'YYYY-MM-DD' %>
      </div>
      <div class='form-group' style="margin-right: 5px;">
        <!-- <label for="end_time">结束时间:</label> -->
        <%= datetime_field_tag :end_time, params[:end_time] || nil, class: 'form-control', style: "margin-right: 5px;", placeholder: 'YYYY-MM-DD' %>
      </div>

      <div class='actions'>
        <%= button_tag '查询', class: 'btn btn-primary' %>
        <%= link_to '重置', '/pun/sys/dashboard/admin_dashboard/user_bills', class: 'btn btn-outline-primary' %>
      </div>
    <% end %>
    <div style="margin-left: 20px;">
      <%= link_to '导出集群报表', 'http://************:8686/api/v5/report/desktop1?from=now-2d&to=now&var-%20job=node_exporter&var-hostname=All&var-node=All&var-maxmount=%2F&var-%20env=&var-name=', target: "_blank", class: 'btn btn-primary' %>
      <%= link_to '导出报表', "/pun/sys/dashboard/admin_dashboard/user_bills?download=true&label=#{params[:label] || "用户账号"}&username=#{params[:username]}&start_time=#{params[:start_time]}&end_time=#{params[:end_time]}", class: 'btn btn-primary' %>
    </div>
  </div>
  <div class="row" style="padding: 20px;">
    <div class="col-md-2"></div>
    <div class="col-md-2">查询账户：<%= params[:username] || '空' %></div>
    <div class="col-md-2"> 账户总共机时：<%= @total_jishi %>/时</div>
    <div class="col-md-2"> 账户总核时：<%= @total_heshi %>/时</div>
    <div class="col-md-2"> 账户总卡时：<%= @total_kashi %>/时</div>
    <div class="col-md-2"></div>
  </div>
  <div class="row">
    <table class="table">
      <thead>
        <tr>
          <th scope="col">账户名称</th>
          <th scope="col">用户名</th>
          <th scope="col">作业名称</th>
          <th scope="col">队列名称</th>
          <th scope="col">机时</th>
          <th scope="col">核时</th>
          <th scope="col">开始时间</th>
          <th scope="col">结束时间</th>
          <th scope="col">使用资源</th>
          <th scope="col">状态</th>
        </tr>
      </thead>
      <tbody>
        <% @bills.each do |bill| %>
          <tr>
            <td><%= bill[:account] %></td>
            <td><%= bill[:user] %></td>
            <td><%= bill[:jobname] %></td>
            <td><%= bill[:partition] %></td>
            <td><%= bill[:elapsed_raw] %></td>
            <td><%= bill[:cputime_raw] %></td>
            <td><%= bill[:start_time] %></td>
            <td><%= bill[:end_time] %></td>
            <td><%= bill[:alloctres] %></td>
            <td><%= bill[:stat] %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%= paginate @bills %>
  </div>
</div>
