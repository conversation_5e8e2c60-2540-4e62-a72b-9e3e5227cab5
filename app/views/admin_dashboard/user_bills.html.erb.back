<div class="container-fluid">
  <div class="row" style="margin-bottom: 10px;">
    <%= form_tag '/pun/sys/dashboard/admin_dashboard/user_bills', method: :get, class: 'form-inline' do |form| %>
      <div class='form-group'>
        <%= select_tag :label, options_for_select([ "用户账号", "备注" ], params[:label] || "用户账号"), class: 'custom-select', style: "margin-right: 5px;" %>
        <%= text_field_tag :username, params[:username] || nil, class: 'form-control', style: "margin-right: 5px;" %>
      </div>

      <div class='actions'>
        <%= button_tag '查询', class: 'btn btn-primary' %>
        <%= link_to '重置', '/pun/sys/dashboard/admin_dashboard/user_bills', class: 'btn btn-outline-primary' %>
      </div>
    <% end %>
    <div style="margin-left: 20px;">
      <%= link_to '导出集群报表', 'http://************:8686/api/v5/report/desktop1?from=now-2d&to=now&var-%20job=node_exporter&var-hostname=All&var-node=All&var-maxmount=%2F&var-%20env=&var-name=', target: "_blank", class: 'btn btn-primary' %>
      <%= link_to '导出报表', '/pun/sys/dashboard/admin_dashboard/user_bills?download=true', class: 'btn btn-primary' %>
    </div>
  </div>
  <div class="row">
    <table class="table">
      <thead>
        <tr>
          <th scope="col">用户账号</th>
          <th scope="col">JobID</th>
          <th scope="col">JobName</th>
          <th scope="col">Status</th>
          <th scope="col">Partition</th>
          <th scope="col">Nodelist</th>
          <th scope="col">Elapsed</th>
          <th scope="col">CPUTime</th>
          <th scope="col">AllocTRES</th>
        </tr>
      </thead>
      <tbody>
        <% @bills.each do |bill| %>
          <tr>
            <td><%= bill[:account] %></td>
            <td><%= bill[:job_id] %></td>
            <td><%= bill[:job_name] %></td>
            <td><%= bill[:state] %></td>
            <td><%= bill[:partition] %></td>
            <td><%= bill[:nodelist] %></td>
            <td><%= bill[:elapsed] %></td>
            <td><%= bill[:cputime] %></td>
            <td><%= bill[:alloctres] %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%= paginate @bills %>
  </div>
</div>

