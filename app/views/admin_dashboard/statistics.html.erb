<%= javascript_include_tag 'echarts' %>

<style>
  .card-body {
    margin-top: 16px;
  }
</style>

<div class="container-fluid">
  <div class="row" style="margin-left: 8%;font-size: 20px;color:#004779;margin-bottom: 10px;">Cluster 集群状态</div>
  <div class="row">
    <div class="col-md-6">
      <div class="row m-auto" style="width: 564px;">
        <div class="col-md-5 card">
          <div class="row">
            <div class="col-md-12 card" style="height: 250px;">
              <div class="card-body">
                <div><span style="font-size: 20px;color:#004779;">机器状态</span></div>
                <div><span style="margin-right: 15px;font-size: 26px;color:#0ad142;">●</span> 正常 <span style="margin-left: 22px;">(<%= @idle_nodes_count %>)</span></div>
                <div><span style="margin-right: 15px;font-size: 26px;color:#e4eb17;">●</span> 故障 <span style="margin-left: 22px;">(<%= @drain_nodes_count %>)</span></div>
                <div><span style="margin-right: 15px;font-size: 26px;color:#df211b;">●</span> 宕机 <span style="margin-left: 22px;">(<%= @down_nodes_count %>)</span></div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 card" style="height: 250px;">
              <div class="card-body">
                <div><span style="font-size: 20px;color:#004779;">CPU使用率</span></div>
                <div style="margin-top: 22px;"><span style="font-size: 36px;color:#004779;"><%= @cpu_usage.to_i %> %</span></div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 card" style="height: 250px;">
              <div class="card-body">
                <div><span style="font-size: 20px;color:#004779;">内存使用率</span></div>
                <div style="margin-top: 22px;"><span style="font-size: 36px;color:#004779;"><%= @memory_usage.to_i %> %</span></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-7 card">
          <div class="row">
            <div class="col-md-12">
              <div id="health" style="width:100%;height:370px;"></div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div id="cpu_mem_usage" style="width:100%;height:300px;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row" style="width: 564px;">
        <div class="col-md-5 card">
          <div class="row">
            <div class="col-md-12 card" style="height: 250px;">
              <div class="card-body">
                <div><span style="font-size: 20px;color:#004779;">数据目录</span></div>
                <div><span style="font-size: 20px;color:#004779;">/home/<USER>/span></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-7 card">
          <div class="row">
            <div class="col-md-12">
              <div id="storage" style="width:100%;height:375px;"></div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div id="storage_usage" style="width:100%;height:300px;margin-bottom:75px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  var chartDom = document.getElementById('health');
  var myChart = echarts.init(chartDom);
  var option;

  option = {
    title: {
      text: '集群健康状态',
      x: 'center',
      y: 'bottom'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: <%= @idle_nodes_count %>, name: '健康\n<%= @idle_nodes_count %>', itemStyle: {color:'#5087EC'} },
          { value: <%= @drain_nodes_count.to_i + @down_nodes_count.to_i %>, name: '故障\n<%= @drain_nodes_count.to_i + @down_nodes_count.to_i %>', itemStyle: {color:'#68BBC4'} }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  option && myChart.setOption(option);

  // 储存图表实例
  var chartDomStorage = document.getElementById('storage');
  var myChartStorage = echarts.init(chartDomStorage);
  var option_storage;

  option_storage = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: <%= 100 - @available_storage_usage.to_i %>, name: '已用\n<%= 100 - @available_storage_usage.to_i %>', itemStyle: {color:'#5087EC'}},
          { value: <%= @available_storage_usage.to_i %>, name: '可用\n<%= @available_storage_usage.to_i %>', itemStyle: {color:'#68BBC4'} }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  option_storage && myChartStorage.setOption(option_storage);

  // CPU内存图表折线图
  var chartDom = document.getElementById('cpu_mem_usage');
  var myChart = echarts.init(chartDom);
  var option;

  option = {
    title: {
      text: 'CPU使用率 内存使用率',
      x: 'center',
      y: 'bottom'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: <%= raw @title_arr %>
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'CPU',
        type: 'line',
        stack: 'Total',
        data: <%= raw @cpu_usage_rencent %>
      },
      {
        name: '内存',
        type: 'line',
        stack: 'Total',
        data: <%= raw @memory_usage_rencent %>
      }
    ]
  };

  option && myChart.setOption(option);

  // 存储使用率折线图
  var chartDom = document.getElementById('storage_usage');
  var myChart = echarts.init(chartDom);
  var option;

  option = {
    title: {
      text: '存储使用率',
      x: 'center',
      y: 'bottom'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: <%= raw @title_arr %>
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '存储',
        type: 'line',
        stack: 'Total',
        data: <%= raw @available_storage_rencent %>
      }
    ]
  };

  option && myChart.setOption(option);
</script>
