<div class="container-fluid">
  <div class="row" style="margin-bottom: 10px;">
    <div class="col-md-6">
      <input type="file" id="files" onchange="upload(event)" hidden/>
      <button class='btn btn-primary import-license' data-toggle="modal" data-target="#addUser">带入许可</button>
    </div>
    <div class="col-md-6">
      <span style="float: right;">当前MAC地址：<%= @mac_address %></span>
    </div>
  </div>
  <div class="row">
    <table class="table">
      <thead>
        <tr>
          <th scope="col">名称</th>
          <th scope="col">导入时间</th>
          <th scope="col">到期时间</th>
          <th scope="col">许可状态</th>
          <th scope="col">操作</th>
        </tr>
      </thead>
      <tbody>
        <% @license_list.each do |license| %>
          <tr>
            <td><%= license[:name] %></td>
            <td><%= license[:import_time] %></td>
            <td><%= license[:due_time] %></td>
            <td><%= license[:status] %></td>
            <td>
              <%= link_to '删除', "/pun/sys/dashboard/admin_dashboard/license_delete?file_name=#{license[:name]}", method: :delete, data: { confirm: '确定删除许可？' } %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%#= paginate @license_list %>
  </div>
</div>

<script type="text/javascript">
  $('.import-license').click(function() {
    $('#files').click();
  });
  function upload(event) {
    const formData = new FormData();
    formData.append('file', event.target.files[0]);
    fetch('/pun/sys/dashboard/admin_dashboard/license_upload', {
      method: 'post',
      body: formData,
      }).then(response => response.json())
      .then((data) => {
        if (data.success == true) {
          alert(data.message);
          window.location.reload();
        } else {
          alert(data.message);
          window.location.reload();
        }
      });
     }
</script>
