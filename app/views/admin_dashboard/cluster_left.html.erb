<div class="container-fluid">
  <div class="row" style="margin-bottom: 10px;">
    <div style="margin-left: 20px;">
      <button class='btn btn-primary start-node' data-toggle="modal" data-target="#addUser">打开节点</button>
      <button class='btn btn-primary stop-node' data-toggle="modal" data-target="#authUser">关闭节点</button>
      <button class='btn btn-primary reset-node' data-toggle="modal" data-target="#authUser">重启节点</button>
      <button class='btn btn-primary uid-node' data-toggle="modal" data-target="#authUser">标记节点</button>
    </div>
  </div>
  <div class="row">
    <table class="table">
      <thead>
        <tr>
          <th scope="col"></th>
          <th scope="col">名称</th>
          <th scope="col">IP</th>
          <th scope="col">BMC</th>
          <th scope="col">节点状态</th>
        </tr>
      </thead>
      <tbody>
        <% @nodes.each do |node| %>
          <tr>
            <td><input type="checkbox" value="<%= node[:name] %>"></td>
            <td><%= node[:name] %></td>
            <td><%= node[:ip] %></td>
            <td><%= node[:bmc] %></td>
            <td><%= node[:state] %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <%= paginate @nodes %>
  </div>
</div>
<script>
  $(".start-node").click(function() {
    changeNode("start");
  });

  $(".stop-node").click(function() {
    changeNode("stop");
  });

  $(".reset-node").click(function() {
    changeNode("reset");
  });

  $(".uid-node").click(function() {
    changeNode("uid");
  });

  function changeNode(state) {
    var arr = new Array();
    $("input:checkbox:checked").each(function() {
      arr.push($(this).val()); //向数组中添加元素
    }); //获取界面复选框的所有值

    $.ajax({
      url: "/pun/sys/dashboard/admin_dashboard/change_node",
      type: "POST",
      data: {
        "nodes": arr,
        "state": state
      },
      success: function(data) {
        if (data.status == "success") {
          alert(data.message);
          window.location.reload();
        } else {
          alert(data.message);
        }
      }
    });
  }
</script>

