<% if flash[:success].present? %>
  <div class="alert alert-success" role="alert">
    <%= flash[:success] %>
  </div>
<% elsif flash[:error].present? %>
  <div class="alert alert-danger" role="alert">
    <%= flash[:error] %>
  </div>
<% end %>
<div class="container-fluid">
  <div class="row" style="margin-bottom: 10px;">
    <%= form_tag '/pun/sys/dashboard/admin_dashboard/user_manage', method: :get, class: 'form-inline' do |form| %>
      <div class='form-group'>
        <%= select_tag :label, options_for_select([ "用户账号", "备注" ], params[:label] || "用户账号"), class: 'custom-select', style: "margin-right: 5px;" %>
        <%= text_field_tag :username, params[:username] || nil, class: 'form-control', style: "margin-right: 5px;" %>
      </div>

      <div class='actions'>
        <%= button_tag '查询', class: 'btn btn-primary' %>
        <%= link_to '重置', '/pun/sys/dashboard/admin_dashboard/user_manage', class: 'btn btn-outline-primary' %>
      </div>
    <% end %>
    <div style="margin-left: 20px;">
      <button class='btn btn-primary' data-toggle="modal" data-target="#addUser">创建用户</button>
      <button class='btn btn-primary' data-toggle="modal" data-target="#addAccount">创建账户</button>
      <button class='btn btn-primary' data-toggle="modal" data-target="#deleteAccount">删除账户</button>
      <button class='btn btn-primary' data-toggle="modal" data-target="#authUser">添加权限</button>
      <button class='btn btn-primary' data-toggle="modal" data-target="#freezeUser">冻结账户</button>
      <button class='btn btn-primary' data-toggle="modal" data-target="#auditAuth">权限审批</button>
    </div>
  </div>
  <div class="row">
    <table class="table">
      <thead>
        <tr>
          <th scope="col">用户账号</th>
          <th scope="col">UID</th>
          <th scope="col">GID</th>
          <th scope="col">家目录</th>
          <th scope="col">所属账户</th>
          <th scope="col">备注</th>
          <th scope="col">操作</th>
        </tr>
      </thead>
      <tbody>
        <% @query_user_list.each do |user| %>
          <% user = JSON.parse(user) %>
          <% user_info = UserInfo.find_by(username: user["uid"]) %>
          <% account_name = CustomLdap.query_account(user["uid"]) %>
          <tr>
            <td><%= user["sn"] %></td>
            <td><%= user["uidnumber"] %></td>
            <td><%= user["gidnumber"] %></td>
            <td><%= user["homedirectory"] %></td>
            <td><%= account_name %></td>
            <td><%= user_info&.remark || "无" %></td>
            <td>
              <button class='btn btn-warning' data-toggle="modal" data-target="#changeUser_<%= user["uid"] %>">修改</button>
              <%= link_to '删除', "/pun/sys/dashboard/admin_dashboard/delete_user?username=#{user["uid"]}", method: :delete, data: { confirm: "请确认是否删除这个用户\: " }, class: 'btn btn-danger' %>
            </td>
          </tr>
          <div class="modal fade" id="changeUser_<%= user["uid"] %>" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">修改系统登录用户</h5>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                </div>
                <div class="modal-body" style="text-align: center;">
                  <%= form_tag '/pun/sys/dashboard/admin_dashboard/change_user' do %>
                    <div class="form-group row">
                      <label for="username" class="col-sm-2 col-form-label">用户名</label>
                      <div class="col-sm-10">
                        <input type="text" class="form-control" id="username" name="username" value="<%= user["uid"] %>" required="required" placeholder="请输入用户名">
                      </div>
                    </div>
                    <div class="form-group row">
                      <label for="new_password" class="col-sm-2 col-form-label">新密码</label>
                      <div class="col-sm-10">
                        <input type="text" class="form-control" id="new_password" name="new_password" placeholder="请输入新密码">
                      </div>
                    </div>
                    <div class="form-group row">
                      <label for="remark" class="col-sm-2 col-form-label">备注</label>
                      <div class="col-sm-10">
                        <input type="text" class="form-control" id="remark" name="remark" value="<%= user_info&.remark %>" placeholder="请输入备注">
                      </div>
                    </div>

                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">确认</button>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </tbody>
    </table>
    <%= paginate @query_user_list %>
  </div>
</div>

<div class="modal fade" id="addAccount" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">创建账户</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="text-align: center;">
        <%= form_tag '/pun/sys/dashboard/admin_dashboard/add_account' do %>
          <div class="form-group row">
            <label for="username" class="col-sm-2 col-form-label">账户名</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="username" name="username" required="required" placeholder="请输入...">
            </div>
          </div>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">确认</button>
        <% end %>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="deleteAccount" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">删除账户</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="text-align: center;">
        <%= form_tag '/pun/sys/dashboard/admin_dashboard/delete_account' do %>
          <div class="form-group row">
            <label for="account_name" class="col-sm-2 col-form-label">账户名</label>
            <div class="col-sm-10">
              <%= select_tag :account_name, options_for_select(UserInfo.all_account), class: "form-control" %>
              <!-- <input type="text" class="form-control" id="account_name" name="username" required="required" placeholder="请输入..."> -->
            </div>
          </div>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">确认</button>
        <% end %>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="addUser" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">系统登录用户创建</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="text-align: center;">
        <%= form_tag '/pun/sys/dashboard/admin_dashboard/add_user' do %>
          <div class="form-group row">
            <label for="username" class="col-sm-2 col-form-label">用户名</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="username" name="username" required="required" placeholder="请输入用户名">
            </div>
          </div>
          <div class="form-group row">
            <label for="password" class="col-sm-2 col-form-label">密码</label>
            <div class="col-sm-10">
              <input type="password" class="form-control" id="password" name="password" required="required" placeholder="请输入密码">
            </div>
          </div>
          <div class="form-group row">
            <label for="home" class="col-sm-2 col-form-label">家目录</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="home" name="home" required="required" placeholder="请输入家目录">
            </div>
          </div>
          <div class="form-group row">
            <label for="remark" class="col-sm-2 col-form-label">备注</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="remark" name="remark" placeholder="请输入备注">
            </div>
          </div>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">确认创建</button>
        <% end %>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="freezeUser" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">冻结账户</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="text-align: center;">
        <%= form_tag '/pun/sys/dashboard/admin_dashboard/freeze_user' do %>
          <div class="form-group row">
            <label for="username" class="col-sm-2 col-form-label">用户名</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="username" name="username" required="required" placeholder="请输入用户名">
            </div>
          </div>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">确认</button>
        <% end %>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="authUser" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">授权账户</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="text-align: center;">
        <%= form_tag '/pun/sys/dashboard/admin_dashboard/auth_user' do %>
          <div class="form-group row">
            <label for="username" class="col-sm-2 col-form-label">用户名</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="username" name="username" required="required" placeholder="请输入用户名">
            </div>
          </div>
          <div class="form-group row">
            <label for="account_name" class="col-sm-2 col-form-label">记账账户</label>
            <div class="col-sm-10">
              <%= select_tag :account_name, options_for_select(UserInfo.all_account), class: "form-control" %>
            </div>
          </div>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">确认</button>
        <% end %>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="changeUser" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">修改系统登录用户</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="text-align: center;">
        <%= form_tag '/pun/sys/dashboard/admin_dashboard/change_user' do %>
          <div class="form-group row">
            <label for="username" class="col-sm-2 col-form-label">用户名</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="username" name="username" required="required" placeholder="请输入用户名">
            </div>
          </div>
          <div class="form-group row">
            <label for="new_password" class="col-sm-2 col-form-label">新密码</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="new_password" name="new_password" required="required" placeholder="请输入新密码">
            </div>
          </div>
          <div class="form-group row">
            <label for="remark" class="col-sm-2 col-form-label">备注</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="remark" name="remark" placeholder="请输入备注">
            </div>
          </div>

          <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">确认</button>
        <% end %>
      </div>
    </div>
  </div>
</div>
