<% if has_ganglia(data.cluster) || has_grafana(data.cluster) %>

<div class="card ml-5 mt-3">

        <% if has_ganglia(data.cluster) %>
          <div class="card-header">Node: <%= node %> <span class="float-right">Job: <%= data.pbsid %> </span></div>
          <div class="card-body">
        
            <%= link_to  image_tag( build_ganglia_link(data.cluster, data.starttime, 'cpu_report', node, 'small'), class:"img-responsive col-lg-auto col-md-auto col-sm-auto col-xs-auto" ), build_ganglia_link(data.cluster, data.starttime, 'cpu_report', node, 'large'), data: { lightbox: "cpu-report", title: "CPU Report " + node } %>
            <%= link_to  image_tag( build_ganglia_link(data.cluster, data.starttime, 'load_report', node, 'small'), class:"img-responsive col-lg-auto col-md-auto col-sm-auto col-xs-auto" ), build_ganglia_link(data.cluster, data.starttime, 'load_report', node, 'large'), data: { lightbox: "load-report", title: "Load Report " + node } %>
            <%= link_to  image_tag( build_ganglia_link(data.cluster, data.starttime, 'mem_report', node, 'small'), class:"img-responsive col-lg-auto col-md-auto col-sm-auto col-xs-auto" ), build_ganglia_link(data.cluster, data.starttime, 'mem_report', node, 'large'), data: { lightbox: "mem-report", title: "Memory Report " + node } %>
            <%= link_to  image_tag( build_ganglia_link(data.cluster, data.starttime, 'network_report', node, 'small'), class:"img-responsive col-lg-auto col-md-auto col-sm-auto col-xs-auto" ), build_ganglia_link(data.cluster, data.starttime, 'network_report', node, 'large'), data: { lightbox: "network-report", title: "Network Report " + node } %>
          </div>
        <% elsif has_grafana(data.cluster) %>
          <div class="card-header">
            Node: <%= node %>
            <span class="ml-3">Job: <%= data.pbsid %></span>
            <span class="ml-3">
              <a href="<%= build_grafana_link(data.cluster, data.starttime, 'node', node) %>" target="_blank">
                    <span class="fa fa-external-link-square-alt"></span> Detailed Metrics
              </a>
            </span>
          </div>
          <div class="card-body">
            <iframe src="<%= build_grafana_link(data.cluster, data.starttime, 'cpu', node, data.pbsid) %>" width="450" height="200" frameborder="0"></iframe>
            <iframe src="<%= build_grafana_link(data.cluster, data.starttime, 'memory', node, data.pbsid) %>" width="450" height="200" frameborder="0"></iframe>
          </div>
         
        <% end %>
  
</div>
       

<% end %>