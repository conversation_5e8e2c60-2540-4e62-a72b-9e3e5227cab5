<% if data.error %>
  <div class="alert alert-warning" role="alert">
    <%= data.error %>
  </div>
<% else %>
<div class="card ml-5">
  <div class="card-header">
    <%# FIXME: ul/li with css %>
    <strong>
      <%= data.status %>
      <span class="text-break ml-3"><%= data.jobname %></span>
      <span class="ml-3"><%= data.pbsid %></span>
    </strong>
  </div>
  <div class="card-body">
    <table class="table table-sm table-striped">
      <tbody>
      <% data.native_attribs.each do |attrib| %>
        <tr>
          <!-- FIXME: col-xs-2... -->
        <td class="col-xs-2"><strong><%= attrib.name %></strong></td>
        <td class="col-xs-10"><%= attrib.value %></td>
      </tr>
    <% end %>
      </tbody>
    </table>
  </div>
    <% if data.submit_args %>
    <div class="card-body">
      <p class="card-text">提交参数: <pre class="card bg-light p-2" style="white-space: pre-wrap;"><%= data.submit_args %></pre></p>
    </div>
    <% end %>
    <% if data.output_path %>
    <div class="card-body">
      <%# FIXME: can use form element input with label and disabled %>
      <p class="card-text">输出位置: <pre class="card bg-light p-2" style="white-space: pre-wrap;"><%= data.output_path %></pre></p>
    </div>
    <% end %>
    <div class="card-body">
      <% if ENV["USER"] == data.username %>
          <%= link_to "#{icon("fas", "folder-open")} 打开文件管理".html_safe, data.file_explorer_url, :class => "btn btn-outline-black", :target => "_blank", :style => "margin: 10px;" if data.file_explorer_url %>
          <%= link_to "#{icon("fas", "terminal")} 打开终端".html_safe, data.shell_url, :class => "btn btn-outline-black", :target => "_blank", :style => "margin: 10px;" if data.shell_url %>
          <%= link_to "#{icon("fas", "trash-o")} 删除".html_safe, delete_job_path(pbsid: data.pbsid, cluster: data.cluster), :class => "btn btn-outline-danger pull-right", :style => "margin: 10px;", data: { method: "delete", confirm: "Are you sure you want to delete #{data.pbsid}" } %>
      <% end %>
    </div>
  </div>
</div>
<% end %>
