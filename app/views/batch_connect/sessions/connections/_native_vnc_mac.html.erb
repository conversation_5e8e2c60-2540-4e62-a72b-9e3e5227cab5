<ol>
  <li>Open a terminal window</li>
  <li>
    <% if Configuration.native_vnc_login_host %>
    <p>
      Copy/paste in your terminal to establish the SSH tunnel:
    </p>
    <pre><code>ssh -f -N -L <%= localport %>:<%= connect.host %>:<%= connect.port %> <%= ENV["USER"] %>@<%= Configuration.native_vnc_login_host %></code></pre>
    <% else %>
    <p>
      Copy/paste in your terminal and replace <code>SSH_HOST</code> with a
      valid HPC login server to establish the SSH tunnel:
    </p>
    <pre><code>ssh -f -N -L <%= localport %>:<%= connect.host %>:<%= connect.port %> <%= ENV["USER"] %>@<strong>SSH_HOST</strong></code></pre>
    <% end %>
  </li>
  <% if browser.platform == :mac %>
    <li>
      <p>Use the code below to establish the VNC connection:</p>
      <pre>open vnc://:<%= connect.password %>@localhost:<%= localport %></pre>
    </li>
  <% else %>
    <li>
      <p>
        Open a VNC client and connect to
        <code>localhost:<%= localport %></code> within the client
      </p>
    </li>
    <li>
      <p>Use the VNC password: <code><%= connect.password %></code></p>
    </li>
  <% end %>
</ol>
