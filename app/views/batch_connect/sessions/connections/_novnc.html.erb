<%= bootstrap_form_tag(url: asset_path("noVNC-1.1.0/vnc.html"), method: "get") do |f| %>
 <%= hidden_field_tag(:autoconnect, 'true') %>
 <%= hidden_field_tag(:path, "rnode/#{connect.host}/#{connect.websocket}/websockify") %>
 <%= hidden_field_tag(:resize, "remote") %>
 <%= hidden_field_tag(:password, connect.password) %>

 <div class="row">
  <div class="col-sm-6">
   <%= f.number_field(:compressionsetting, class: 'custom-range', type: 'range', min: 0, max: 9, value: 6, label: "Compression", help: "0 (low) to 9 (high)") %>
  </div>
  <div class="col-sm-6">
   <%= f.number_field(:qualitysetting, class: 'custom-range', type: 'range', min: 0, max: 9, value: 2, label: "Image Quality", help: "0 (low) to 9 (high)") %>
  </div>
 </div>
 <script type="text/javascript">
  // Functions defined in application.js
  for(var name of ['compressionsetting', 'qualitysetting']) {
    tryUpdateSetting(name);
    installSettingHandlers(name);
  }
 </script>

 <%= f.submit( t('dashboard.batch_connect_sessions_novnc_launch', app_title: app_title), class: 'btn btn-primary', :formtarget => "_blank") %>
 <%= link_to t('dashboard.batch_connect_sessions_novnc_view_only'), novnc_link(connect, view_only: true), class: 'btn btn-light float-right', target: '_blank' %>
<% end %>
