<% content_for :title, "#{t('dashboard.breadcrumbs_my_sessions')} - #{OodAppkit.dashboard.title}" %>
<%-
  any_apps = (@sys_app_groups + @usr_app_groups + @dev_app_groups).any?
-%>

<%= render partial: 'batch_connect/shared/breadcrumb',
locals: {
  links: [
  {
    text: t('dashboard.breadcrumbs_home'),
    href: root_path
  },
  {
    text: t('dashboard.breadcrumbs_my_sessions')
  }]
}
%>

<div class="row">
  <%- if any_apps -%>
    <div class="col-md-3">
      <%=
        render(
          partial: "batch_connect/shared/app_menu",
          locals: {
            sys_app_groups: @sys_app_groups,
            usr_app_groups: @usr_app_groups,
            dev_app_groups: @dev_app_groups
          }
        )
      %>
    </div>
    <div class="col-md-9">
      <div class="batch-connect sessions" data-toggle="poll" data-url="<%= batch_connect_sessions_path(format: :js) %>" data-delay="<%= ENV["POLL_DELAY"] || 10000 %>">
        <% if @sessions.empty? %>
          <div class="ood-appkit markdown">
            <p><%= t('dashboard.batch_connect_no_sessions') %></p>
          </div>
        <% else %>
          <%= render partial: "panel", collection: @sessions, as: :session %>
        <% end %>
      </div>
    </div>
  <%- else -%>
    <div class="col-md-12">
      <div class="ood-appkit markdown">
        <p><%= t('dashboard.batch_connect_no_apps') %></p>
      </div>
    </div>
  <%- end -%>
</div>