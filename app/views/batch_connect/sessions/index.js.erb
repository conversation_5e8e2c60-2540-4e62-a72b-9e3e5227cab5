(function() {
  var sessions = [];
  var $target;

  <%# Replace sessions whose state has changed %>
  <% @sessions.each do |session| %>
    sessions.push('<%= session.id %>');
    $target = $('#id_<%= session.id %>');
    if ( $target.data('hash') !== '<%= session.to_hash %>' ) {
      $target.replaceWith('<%= j render(partial: "panel", locals: { session: session }) %>');
    }
  <% end %>

  <%# Remove sessions that don't exist anymore %>
  $('.session-panel').each(function () {
    if ( $.inArray($(this).attr('data-id'), sessions) < 0 ) {
      $(this).remove();
    }
  });
})();
