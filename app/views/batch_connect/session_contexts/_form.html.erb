<div style="padding-left: 10px;padding-right: 10px;">
  <%= bootstrap_form_for(@session_context, html: { id: 'batch-connect-form' }) do |f| %>
    <% f.object.each do |attrib| %>
      <%= create_widget(f, attrib, format: @render_format) %>
    <% end %>

    <%= f.submit "加入队列", class: "btn btn-primary-another btn-block " %>
  <% end %>
</div>
<%= javascript_pack_tag 'batchConnect' if Configuration.bc_dynamic_js? %>

<% @app.custom_javascript_files.each do |jsfile| %>
  <%= javascript_tag "(function(){\n" + jsfile.read + "\n}());" %>
<% end %>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('batch-connect-form');

  if (form) {
    form.addEventListener('submit', function(event) {
      // 等待表单提交完成后关闭模态框
      setTimeout(function() {
        const closeButton = window.parent.document.getElementById('close-app-modal');
        if (closeButton) {
          closeButton.click();
        }
      }, 100); // 短暂延迟确保提交处理完成
    });
  }
});
</script>
