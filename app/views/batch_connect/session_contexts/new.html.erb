<style>
  .input-placeholder-light {
    border-color:#e5e5e5;
    width: 100%;
    font-size: 14px;
    padding: 23px 16px;
    background:#f9f9f9;
    ::placeholder{color:#bfbfbf;opacity:1;}
  }

  .submit-button {
    font-size: 14px;
    padding: 12px;
    background: #007Aff;
  }

  .btn-primary-another {
    background-color: #007Aff;
    color: white;
  }

  .btn-primary-another:hover {
    color: white;
  }

  .btn-danger-another {
    background-color: rgba(255, 73, 73, 0.1);
    color: #FF4949;
  }

  .btn-danger-another:hover {
    background-color: rgba(255, 73, 73, 0.1);
    color: #FF4949;
  }

  .input-placeholder-light::placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light::-webkit-input-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light::-moz-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light:-ms-input-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light::-ms-input-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }

  input.form-control {
    border-radius: 6px;
  }

  input.form-control:focus,
  textarea.form-control:focus {
    border-color: #007Aff !important;
    border-width: 1px;
    box-shadow: none;
    outline: none;
  }
</style>

<div class="row">
  <!-- <div class="col-md-3"> -->
    <%#=
      render(
        partial: "batch_connect/shared/app_menu",
        locals: {
          sys_app_groups: @sys_app_groups,
          usr_app_groups: @usr_app_groups,
          dev_app_groups: @dev_app_groups,
          current_url: new_batch_connect_session_context_path(token: @app.token)
        }
      )
    %>
  <!-- </div> -->

  <div class="col-md-12">
    <!-- <h3>
      <%= @app.title %>
      <% if @app.version != 'unknown' %>
        <small>version: <%= @app.version %></small>
      <% end %>
    </h3> -->
    <div>模板介绍：</div>
    <div class="ood-appkit markdown">
      <%= OodAppkit.markdown.render(@app.description).html_safe %>

      <%- if @session_context -%>
        <%= render "form" %>
        <p>
          <%#= t('dashboard.batch_connect_form_session_data_html',
                title: @app.title,
                data_link_tag: link_to(
                  t('dashboard.batch_connect_form_data_root'),
                  OodAppkit.files.url(
                    path: BatchConnect::Session.dataroot(@app.token)
                  ).to_s,
                  target: "_blank")
                )
          %>
        </p>
      <%- end -%>
    </div>
  </div><!-- /.col-md-6 -->
</div><!-- /.row -->
