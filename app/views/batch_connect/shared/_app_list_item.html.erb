<% app.links.each do |link| %>
  <%=
    link_to(
      link.url.to_s,
      class: "list-group-item list-group-item-action #{"active" if local_assigns[:current_url] == link.url}",
      data: {
        toggle: "popover",
        content: manifest_markdown(link.description),
        html: true,
        trigger: "hover",
        title: link.title,
        container: "body"
      }
    ) do
  %>
    <%= icon_tag(link.icon_uri) %> <%= link.title %>
    <%- if show_owner -%>
      <small>(<%= app.owner %>)</small>
    <%- end -%>
  <% end %>
<% end %>