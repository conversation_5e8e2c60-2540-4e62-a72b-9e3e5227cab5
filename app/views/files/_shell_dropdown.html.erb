<div id="shell-wrapper" class="btn-group dropright">
  <%= link_to OodAppkit.shell.url(path: @path.to_s).to_s, id: 'open-in-terminal-btn', class: 'btn btn-outline-dark btn-sm', target: '_blank' do %>
    <i class="fas fa-terminal" aria-hidden="true"></i>
    Open in Terminal
  <% end %>
  <% if Configuration.login_clusters.count > 0 %>
    <button type="button" class="btn btn-sm btn-outline-dark dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>
    <ul class="dropdown-menu" x-placement="right-start" id="shell-dropdown-items">
      <% Configuration.login_clusters.each do |cluster| %>
        <%=
          tag.li(
            tag.a(
              "#{cluster.metadata.title || cluster.id.to_s.titleize}",
              href: OodAppkit.shell.url(host: cluster.login.host, path: @path.to_s),
              title: "#{cluster.metadata.title || cluster.id.to_s.titleize}",
              target: '_blank',
              class: 'dropdown-item'
            )
          )
        %>
      <% end %>
    </ul>
  <% end %>
</div>
