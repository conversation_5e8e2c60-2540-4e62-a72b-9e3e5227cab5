// Place all Bootstrap 4 variable overrides in this file.
// Bootstrap 4 SASS variables list: https://github.com/twbs/bootstrap/blob/v4.6.0/scss/_variables.scss

// Miscellaneous
$enable-responsive-font-sizes: "true";
$enable-shadows: "true";

// Colors
$blue: rgb(51, 122, 183);
$green: #5cb85c;
$body-color: rgb(51, 51, 51);

// see https://getbootstrap.com/docs/4.0/getting-started/theming/#modify-map
$theme-colors: (
  "primary": $blue,
  "success": $green,
  "danger": #d9534f
);

// NOTE: bootstrap 3 info color was not teal but #5bc0de
// however bootstrap's calculation to determine the text ends up with black instead of white text
// so we avoid overriding this

// Navbar color default overrides
$navbar-light-color: rgb(119, 119, 119);
$navbar-light-disabled-color: rgb(119, 119, 119);
$navbar-dark-brand-color: rgb(119, 119, 119);

$navbar-dark-color: #fff;
$navbar-dark-active-color: rgba(#fff, .75);
$navbar-dark-brand-color: #fff;
$navbar-dark-disabled-color: rgba(#fff, .75);

// Grid layout
$container-max-widths: (
  xs: 0,
  sm: 768px,
  md: 920px,
  lg: 1140px,
  xl: 1260px,
);

$grid-breakpoints: (
  xs: 0,
  sm: 676px,
  md: 850px,
  lg: 1460px,
  xl: 1500px
);

// Navigation bar link gutter spacing x-axis.
$navbar-nav-link-padding-x: 0.75rem;

// breadcrumbs are purely visual for accessibility
$breadcrumb-divider: '';
