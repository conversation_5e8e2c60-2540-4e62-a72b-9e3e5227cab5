#directory-contents .selected {
  color: white;
  background-color: rgb(0, 136, 204);
}

#directory-contents .selected a.name {
  color: white;
  font-weight: bold;
}

#directory-contents.dragover {
  border: 5px solid rgb(51, 122, 183) !important;
}

//FIXME: jquery datatables lets us modify layout using "dom" but that doesn't let you specify anything
// other than div elements; so we will merge these into one that can fit in a single div when we use a react component
// that can manage state of both so when one is updated the other remains untouched
//
// the react component can make use of
div.datatables-status, div.transfers-status {
  display: inline;
}
