// this file adds two classes:
//
// 1. navbar-inverse-ood-grey-color 
// 2. navbar-inverse-ood-size-sm
//
// which resets the styles of a bootstrap navbar-inverse nav
// to the OOD default grey AND the height of 20px
// these must be added to a div or header tag ABOVE the actual navbar div
// for them to work

// make navbar the grey default color
// changed from the red color we use for branding
.navbar-inverse-ood-grey-color{
  .navbar-inverse {
    background-color: #53565a;
    border-color: #3b3d3f;
  }

  .navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
    background-color: #3b3d3f;
  }

  .navbar-inverse .navbar-collapse,
  .navbar-inverse .navbar-form {
    border-color: #424447;
  }

  .navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
    background-color: #3b3d3f;
  }

  @media (max-width: 767px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
      border-color: #3b3d3f;
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
      background-color: #3b3d3f;
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
      background-color: #3b3d3f;
    }
  }
}

// make navbar 20px high
.navbar-inverse-ood-size-sm{
  .navbar {
    min-height: 20px;
  }

  .navbar-brand {
    padding-top: 0px;
    padding-bottom: 0px;
    height: 20px;
  }

  .navbar-toggle {
    margin-top: -7px;
    margin-bottom: -7px;
  }

  .navbar-nav {
    margin-top: 0px;
    margin-bottom: 0px;
  }


  @media (min-width: 768px) {
    .navbar-nav > li > a {
      padding-top: 0px;
      padding-bottom: 0px;
    }
  }

  .navbar-form {
    margin-top: -7px;
    margin-bottom: -7px;
  }

  .navbar-btn {
    margin-top: -7px;
    margin-bottom: -7px;
  }
  .navbar-btn.btn-sm, .btn-group-sm > .navbar-btn.btn {
    margin-top: -5px;
    margin-bottom: -5px;
  }
  .navbar-btn.btn-xs, .btn-group-xs > .navbar-btn.btn {
    margin-top: -1px;
    margin-bottom: -1px;
  }
  .navbar-text {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .ood-app.navbar ul.navbar-breadcrumbs > li > a, .ood-app.navbar ul.navbar-breadcrumbs > li + li:before {
    padding-top: 0px;
    padding-bottom: 0px;
    height: 20px;
  }

  .ood-app.navbar ul.navbar-breadcrumbs > li + li:before {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .ood-app.navbar ul.navbar-breadcrumbs > li + li:before {
    padding-top: 0px;
    padding-bottom: 0px;
  }
  body {
    padding-top: 30px;
  }
}
