class UserInfo < ActiveRecord::Base
  validates :username, presence: true

  def self.all_account
    output = `sacctmgr show account`
    # 将输出结果按行分割成数组
    lines = output.split("\n")
    accounts = lines[2..-1].map do |line|
      line.match(/^\s*(\S+)/)[1]
    end
  end

  def avatar_url
    if avatar.present?
      "/avatars/#{avatar}"
    else
      "/avatars/default.svg"
    end
  end

  def has_avatar?
    avatar.present? && File.exist?(Rails.root.join('public', 'avatars', avatar))
  end
end
