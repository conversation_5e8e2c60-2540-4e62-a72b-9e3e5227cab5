class AccountLog < ActiveRecord::Base
  self.table_name = 'account_logs'

  # 扣除余额记录
  def self.deduct_balance(username, amount, title, description)
    # 获取用户当前余额（从最新记录中获取）
    last_record = where(username: username).order(created_at: :desc).first
    current_balance = last_record ? last_record.after_balance : 0.0

    # 计算扣除后的余额
    new_balance = current_balance - amount.abs

    # 创建扣费记录
    create!(
      username: username,
      changed_balance: -amount.abs,
      after_balance: new_balance,
      title: title,
      description: description
    )
  end

  # 新增余额记录
  def self.add_balance(username, amount, title, description)
    # 获取用户当前余额（从最新记录中获取）
    last_record = where(username: username).order(created_at: :desc).first
    current_balance = last_record ? last_record.after_balance : 0.0

    # 计算新增后的余额
    new_balance = current_balance + amount.abs

    # 创建充值记录
    create!(
      username: username,
      changed_balance: amount.abs,
      after_balance: new_balance,
      title: title,
      description: description
    )
  end
end
