class UsageType
  class << self
    def single_usage
      result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=sum\\(node_exporter_build_info\\)`)
      @idle_nodes_count = result["data"]["result"][0]["value"][1]

      result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=slurm_nodes_drain`)
      @drain_nodes_count = result["data"]["result"][0]["value"][1]

      result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=slurm_nodes_down`)
      @down_nodes_count = result["data"]["result"][0]["value"][1]
      [@idle_nodes_count, @drain_nodes_count, @down_nodes_count]
    end

    def total_usage
      @cpu_usage = cpu_usage
      cpu, mem, storage = [], [], []
      title_arr = []
      5.times do |i|
        title_arr << Time.now.months_ago(i).strftime("%Y-%m")
      end
      @title_arr = title_arr.reverse

      5.times do |i|
        cpu << cpu_usage(Time.now.months_ago(i))
      end
      @cpu_usage_rencent = cpu.reverse
      @memory_usage = memory_usage

      5.times do |i|
        mem << memory_usage(Time.now.months_ago(i))
      end
      @memory_usage_rencent = mem.reverse
      @available_storage_usage = storage_usage

      5.times do |i|
        storage << (100 - storage_usage(Time.now.months_ago(i)))
      end
      @available_storage_rencent = storage.reverse
      [@cpu_usage, @cpu_usage_rencent, @memory_usage, @memory_usage_rencent, @available_storage_usage, @available_storage_rencent, @title_arr]
    end

    def cpu_usage(time=nil)
      if time
        result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_load5\\&time=#{time.to_i}`)
      else
        result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_load5`)
      end
      tmp_cpu_usage = 0
      result["data"]["result"].each do |node|
        tmp_cpu_usage += node["value"][1].to_f
      end
      (tmp_cpu_usage * 1.0).to_i
    end

    def memory_usage(time=nil)
      if time
        total_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_memory_MemTotal_bytes\\&time=#{time.to_i}`)
      else
        total_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_memory_MemTotal_bytes`)
      end
      total_memory_usage = 0
      total_result["data"]["result"].each do |node|
        total_memory_usage += node["value"][1].to_f
      end

      if time
        available_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_memory_MemAvailable_bytes\\&time=#{time.to_i}`)
      else
        available_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_memory_MemAvailable_bytes`)
      end

      available_memory_usage = 0
      available_result["data"]["result"].each do |node|
        available_memory_usage += node["value"][1].to_f
      end
      return 0 if available_memory_usage == 0

      (available_memory_usage / total_memory_usage * 1.0).to_i
    end

    def storage_usage(time=nil)
      if time
        available_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_filesystem_avail_bytes\\&time=#{time.to_i}`)
      else
        available_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_filesystem_avail_bytes`)
      end
      available_storage_usage = 0
      available_result["data"]["result"].each do |node|
        if node["metric"]["mountpoint"] == "/home"
          available_storage_usage += node["value"][1].to_f
        end
      end

      if time
        total_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_filesystem_size_bytes\\&time=#{time.to_i}`)
      else
        total_result = JSON.parse(`curl 192.168.0.112:9090/api/v1/query?query=node_filesystem_size_bytes`)
      end

      total_storage_usage = 0
      total_result["data"]["result"].each do |node|
        if node["metric"]["mountpoint"] == "/home"
          total_storage_usage += node["value"][1].to_f
        end
      end
      return 0 if available_storage_usage == 0
      (available_storage_usage / total_storage_usage * 1.0).to_i
    end
  end
end
