require 'openssl'
require 'base64'
require 'json'
require 'time'

class Cipher
  class << self
    def aes_decrypt(data)
      cipher = OpenSSL::Cipher::AES256.new(:CBC)
      cipher.decrypt
      cipher.key = key
      iv = data.slice(0,16)
      data = data.slice(16..-1)
      cipher.iv = iv
      decrypted = cipher.update(data) + cipher.final
      return decrypted
    end

    def verify_license(license_str)
      decrypted_str = Base64.decode64(license_str)
      aes_decrypt(decrypted_str)
    end

    def key
      Rails.application.credentials.license_key
    end

    def get_mac_address
      if BasicInfo.count.zero?
        result_str = `ifconfig`
        mac_regex = /\b([0-9A-Fa-f]{2}:){5}[0-9A-Fa-f]{2}\b/
        mac = result_str.match(mac_regex)[0]
        BasicInfo.create(mac_address: mac)
        mac
      else
        BasicInfo.first.mac_address
      end
    end

    def get_license_list
      license_list = []
      dir_path = "#{Rails.root}/public/license"
      Dir.mkdir(dir_path) unless Dir.exist?(dir_path)

      Dir.foreach(dir_path) do |filename|
        next if filename == "." || filename == ".."
        file_path = "#{dir_path}/#{filename}"
        import_time = Time.parse(`stat -c %y #{file_path}`)
        s = File.read(file_path)
        begin
          result = Cipher.verify_license(s)
          if result.split(",")[0] != get_mac_address
            status = "无效"
            due_time = '-'
          else
            time = result.split(",")[1]
            if time == "-"
              status = "激活"
              due_time = "-"
            else
              due_time = Time.at(time.to_i).to_date
              status = Time.now.to_date > due_time.to_date ? "已过期" : "激活"
            end
          end
        rescue
          status = "无效"
          due_time = '-'
        end
        license_list << {name: filename, import_time: import_time&.to_date, due_time: due_time, status: status}
      end
      license_list
    end
  end
end
